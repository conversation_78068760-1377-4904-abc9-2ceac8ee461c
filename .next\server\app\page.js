/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/i18n-provider.tsx */ \"(ssr)/./providers/i18n-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/session-provider.tsx */ \"(ssr)/./providers/session-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2l2YWhyJTVDJTVDT25lRHJpdmUlNUMlNUNEb2N1bWVudHMlNUMlNUNWQ29kZSUyMHByb2plY3RzJTVDJTVDemFrYXQtZGVlcGFnZW50JTVDJTVDemFrYXRfbWFuYWdlbWVudF9zeXN0ZW0lNUMlNUNhcHAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2l2YWhyJTVDJTVDT25lRHJpdmUlNUMlNUNEb2N1bWVudHMlNUMlNUNWQ29kZSUyMHByb2plY3RzJTVDJTVDemFrYXQtZGVlcGFnZW50JTVDJTVDemFrYXRfbWFuYWdlbWVudF9zeXN0ZW0lNUMlNUNhcHAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpdmFociU1QyU1Q09uZURyaXZlJTVDJTVDRG9jdW1lbnRzJTVDJTVDVkNvZGUlMjBwcm9qZWN0cyU1QyU1Q3pha2F0LWRlZXBhZ2VudCU1QyU1Q3pha2F0X21hbmFnZW1lbnRfc3lzdGVtJTVDJTVDYXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDaXZhaHIlNUMlNUNPbmVEcml2ZSU1QyU1Q0RvY3VtZW50cyU1QyU1Q1ZDb2RlJTIwcHJvamVjdHMlNUMlNUN6YWthdC1kZWVwYWdlbnQlNUMlNUN6YWthdF9tYW5hZ2VtZW50X3N5c3RlbSU1QyU1Q2FwcCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDaXZhaHIlNUMlNUNPbmVEcml2ZSU1QyU1Q0RvY3VtZW50cyU1QyU1Q1ZDb2RlJTIwcHJvamVjdHMlNUMlNUN6YWthdC1kZWVwYWdlbnQlNUMlNUN6YWthdF9tYW5hZ2VtZW50X3N5c3RlbSU1QyU1Q2FwcCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNpdmFociU1QyU1Q09uZURyaXZlJTVDJTVDRG9jdW1lbnRzJTVDJTVDVkNvZGUlMjBwcm9qZWN0cyU1QyU1Q3pha2F0LWRlZXBhZ2VudCU1QyU1Q3pha2F0X21hbmFnZW1lbnRfc3lzdGVtJTVDJTVDYXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQXNNO0FBQ3RNO0FBQ0Esb09BQXVNO0FBQ3ZNO0FBQ0EsME9BQTBNO0FBQzFNO0FBQ0Esd09BQXlNO0FBQ3pNO0FBQ0Esa1BBQThNO0FBQzlNO0FBQ0Esc1FBQXdOIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLz8wODUyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaXZhaHJcXFxcT25lRHJpdmVcXFxcRG9jdW1lbnRzXFxcXFZDb2RlIHByb2plY3RzXFxcXHpha2F0LWRlZXBhZ2VudFxcXFx6YWthdF9tYW5hZ2VtZW50X3N5c3RlbVxcXFxhcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxhcHAtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxpdmFoclxcXFxPbmVEcml2ZVxcXFxEb2N1bWVudHNcXFxcVkNvZGUgcHJvamVjdHNcXFxcemFrYXQtZGVlcGFnZW50XFxcXHpha2F0X21hbmFnZW1lbnRfc3lzdGVtXFxcXGFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxpdmFoclxcXFxPbmVEcml2ZVxcXFxEb2N1bWVudHNcXFxcVkNvZGUgcHJvamVjdHNcXFxcemFrYXQtZGVlcGFnZW50XFxcXHpha2F0X21hbmFnZW1lbnRfc3lzdGVtXFxcXGFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxpdmFoclxcXFxPbmVEcml2ZVxcXFxEb2N1bWVudHNcXFxcVkNvZGUgcHJvamVjdHNcXFxcemFrYXQtZGVlcGFnZW50XFxcXHpha2F0X21hbmFnZW1lbnRfc3lzdGVtXFxcXGFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGl2YWhyXFxcXE9uZURyaXZlXFxcXERvY3VtZW50c1xcXFxWQ29kZSBwcm9qZWN0c1xcXFx6YWthdC1kZWVwYWdlbnRcXFxcemFrYXRfbWFuYWdlbWVudF9zeXN0ZW1cXFxcYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxpdmFoclxcXFxPbmVEcml2ZVxcXFxEb2N1bWVudHNcXFxcVkNvZGUgcHJvamVjdHNcXFxcemFrYXQtZGVlcGFnZW50XFxcXHpha2F0X21hbmFnZW1lbnRfc3lzdGVtXFxcXGFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL2FwcC8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4PzkyODkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzL2Rpc3QvdHlwZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! i18next-browser-languagedetector */ \"(ssr)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\");\n\n\n\nconst resources = {\n    ar: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"لوحة التحكم\",\n            \"profile\": \"الملف الشخصي\",\n            \"requests\": \"الطلبات\",\n            \"tasks\": \"المهام\",\n            \"reports\": \"التقارير\",\n            \"settings\": \"الإعدادات\",\n            \"logout\": \"تسجيل الخروج\",\n            // Authentication\n            \"login\": \"تسجيل الدخول\",\n            \"register\": \"إنشاء حساب\",\n            \"email\": \"البريد الإلكتروني\",\n            \"password\": \"كلمة المرور\",\n            \"full_name\": \"الاسم الكامل\",\n            \"national_id\": \"رقم الهوية الوطنية\",\n            \"phone_number\": \"رقم الهاتف\",\n            \"login_tawtheeq\": \"تسجيل الدخول عبر توثيق\",\n            \"login_success\": \"تم تسجيل الدخول بنجاح\",\n            \"login_error\": \"خطأ في البيانات المدخلة\",\n            // Dashboard Enhancements\n            \"welcome\": \"مرحباً\",\n            \"overview_status\": \"نظرة عامة على حالة\",\n            \"system_overview\": \"النظام\",\n            \"your_requests\": \"طلباتك\",\n            \"total_beneficiaries\": \"إجمالي المستفيدين\",\n            \"total_zakat_distributed\": \"إجمالي الزكاة الموزعة\",\n            \"pending_applications\": \"الطلبات المعلقة\",\n            \"active_distributions\": \"التوزيعات النشطة\",\n            \"quick_actions\": \"الإجراءات السريعة\",\n            \"register_new_beneficiary\": \"تسجيل مستفيد جديد\",\n            \"search_beneficiaries\": \"البحث عن المستفيدين\",\n            \"generate_report\": \"إنشاء تقرير\",\n            \"manage_distributions\": \"إدارة التوزيعات\",\n            \"recent_activity\": \"النشاط الأخير\",\n            \"distribution_overview\": \"نظرة عامة على التوزيع\",\n            \"monthly_distributions\": \"التوزيعات الشهرية\",\n            \"beneficiary_categories\": \"فئات المستفيدين\",\n            \"view_all\": \"عرض الكل\",\n            \"this_month\": \"هذا الشهر\",\n            \"last_month\": \"الشهر الماضي\",\n            \"last_week\": \"الأسبوع الماضي\",\n            \"this_week\": \"هذا الأسبوع\",\n            \"increase\": \"زيادة\",\n            \"decrease\": \"انخفاض\",\n            \"from\": \"من\",\n            \"sar\": \"ريال سعودي\",\n            \"beneficiaries\": \"المستفيدين\",\n            \"applications\": \"طلبات\",\n            \"distributions\": \"توزيعات\",\n            \"completed\": \"مكتمل\",\n            \"pending\": \"معلق\",\n            \"warning\": \"تحذير\",\n            // Profile\n            \"personal_profile\": \"الملف الشخصي\",\n            \"basic_info\": \"المعلومات الأساسية\",\n            \"family_info\": \"بيانات الأسرة\",\n            \"employment_info\": \"بيانات العمل\",\n            \"financial_info\": \"المعلومات المالية\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Assistance Requests\n            \"assistance_request\": \"طلب المساعدة\",\n            \"new_request\": \"طلب جديد\",\n            \"request_type\": \"نوع المساعدة\",\n            \"requested_amount\": \"المبلغ المطلوب\",\n            \"approved_amount\": \"المبلغ المعتمد\",\n            \"description\": \"الوصف\",\n            \"attach_documents\": \"إرفاق المستندات\",\n            \"submit_request\": \"إرسال الطلب\",\n            \"requests_subtitle_applicant\": \"إدارة طلبات المساعدة الخاصة بك\",\n            \"requests_subtitle_reviewer\": \"مراجعة جميع طلبات المساعدة\",\n            \"search_requests_placeholder\": \"البحث في الطلبات...\",\n            \"request_number\": \"رقم الطلب\",\n            \"submission_date\": \"تاريخ التقديم\",\n            \"processing_stages\": \"مراحل المعالجة\",\n            \"no_requests\": \"لا توجد طلبات\",\n            \"no_requests_search\": \"لم يتم العثور على طلبات تطابق البحث\",\n            \"no_requests_applicant\": \"لم تقم بتقديم أي طلبات بعد\",\n            \"no_requests_reviewer\": \"لا توجد طلبات للمراجعة\",\n            \"submit_new_request\": \"تقديم طلب جديد\",\n            // Status\n            \"draft\": \"مسودة\",\n            \"submitted\": \"مرسل\",\n            // Workflow Stages\n            \"reception_review\": \"مراجعة الاستقبال\",\n            \"researcher_review\": \"مراجعة الباحث\",\n            \"banking_expert_review\": \"مراجعة الخبير المصرفي\",\n            \"department_head_review\": \"مراجعة رئيس القسم\",\n            \"admin_manager_review\": \"مراجعة مدير الإدارة\",\n            \"minister_review\": \"مراجعة الوزير\",\n            // Actions\n            \"approve\": \"موافقة\",\n            \"reject\": \"رفض\",\n            \"return\": \"إرجاع\",\n            \"save\": \"حفظ\",\n            \"edit\": \"تعديل\",\n            \"delete\": \"حذف\",\n            \"view\": \"عرض\",\n            \"download\": \"تحميل\",\n            \"upload\": \"رفع\",\n            // Common\n            \"loading\": \"جاري التحميل...\",\n            \"search\": \"بحث\",\n            \"filter\": \"تصفية\",\n            \"date\": \"التاريخ\",\n            \"amount\": \"المبلغ\",\n            \"status\": \"الحالة\",\n            \"notes\": \"الملاحظات\",\n            \"documents\": \"المستندات\",\n            \"history\": \"التاريخ\",\n            // Dashboard Stats\n            \"total_requests\": \"إجمالي الطلبات\",\n            \"pending_review\": \"في انتظار المراجعة\",\n            \"approved_today\": \"موافق عليها اليوم\",\n            \"rejected_today\": \"مرفوضة اليوم\",\n            \"average_processing_days\": \"متوسط أيام المعالجة\",\n            \"total_users\": \"إجمالي المستخدمين\",\n            // User Roles\n            \"zakat_applicant\": \"مقدم طلب الزكاة\",\n            \"reception_staff\": \"موظف الاستقبال\",\n            \"researcher\": \"الباحث\",\n            \"banking_expert\": \"الخبير المصرفي\",\n            \"department_head\": \"رئيس القسم\",\n            \"admin_manager\": \"مدير الإدارة\",\n            \"minister\": \"الوزير\",\n            \"system_admin\": \"مسؤول النظام\",\n            // Admin Navigation\n            \"User Management\": \"إدارة المستخدمين\",\n            \"Assistance Types\": \"أنواع المساعدات\",\n            \"System Settings\": \"إعدادات النظام\",\n            // Admin System\n            \"admin_dashboard\": \"لوحة تحكم الإدارة\",\n            \"system_administration\": \"إدارة النظام\",\n            \"admin_access_required\": \"يتطلب صلاحية المدير\",\n            \"authentication_required\": \"يتطلب تسجيل الدخول\",\n            \"please_login\": \"يرجى تسجيل الدخول\",\n            \"back_to_dashboard\": \"العودة إلى لوحة التحكم\",\n            // Admin Navigation\n            \"admin_dashboard_nav\": \"لوحة تحكم الإدارة\",\n            \"user_management\": \"إدارة المستخدمين\",\n            \"assistance_types\": \"أنواع المساعدات\",\n            \"workflow_management\": \"إدارة سير العمل\",\n            \"document_configuration\": \"إعدادات المستندات\",\n            \"distribution_rules\": \"قواعد التوزيع\",\n            \"system_settings\": \"إعدادات النظام\",\n            \"audit_trail\": \"سجل المراجعة\",\n            \"data_management\": \"إدارة البيانات\",\n            // Admin Descriptions\n            \"system_administration_overview\": \"نظرة عامة على إدارة النظام\",\n            \"manage_user_accounts_roles\": \"إدارة حسابات المستخدمين والأدوار\",\n            \"configure_aid_types_eligibility\": \"تكوين أنواع المساعدات ومعايير الأهلية\",\n            \"configure_approval_workflows\": \"تكوين سير عمل الموافقات\",\n            \"manage_document_requirements\": \"إدارة متطلبات المستندات\",\n            \"configure_application_distribution\": \"تكوين توزيع الطلبات\",\n            \"general_system_configuration\": \"إعدادات النظام العامة\",\n            \"view_system_activity_logs\": \"عرض سجلات نشاط النظام\",\n            \"import_export_backup\": \"استيراد/تصدير ونسخ احتياطي\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"إدارة المستفيدين\",\n            \"beneficiary_management_desc\": \"إدارة وتتبع المستفيدين من الزكاة والمساعدات\",\n            \"beneficiary_registration\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration_desc\": \"إضافة مستفيد جديد إلى نظام إدارة الزكاة\",\n            \"beneficiary_list\": \"قائمة المستفيدين\",\n            \"beneficiary_profile\": \"الملف الشخصي للمستفيد\",\n            \"beneficiary_details\": \"تفاصيل المستفيد\",\n            \"beneficiary_not_found\": \"المستفيد غير موجود\",\n            \"beneficiary_not_found_desc\": \"لم يتم العثور على المستفيد المطلوب\",\n            \"back_to_beneficiaries\": \"العودة إلى قائمة المستفيدين\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"المعتمدين\",\n            \"under_review_beneficiaries\": \"قيد المراجعة\",\n            \"total_distributions\": \"إجمالي التوزيعات\",\n            \"average_distribution\": \"متوسط التوزيع\",\n            \"pending_verification_count\": \"في انتظار التحقق\",\n            \"needs_review\": \"يحتاج إلى مراجعة\",\n            \"of_total\": \"من الإجمالي\",\n            \"per_beneficiary\": \"للمستفيد\",\n            // Search and Filters\n            \"search_and_filter\": \"البحث والتصفية\",\n            \"search_placeholder\": \"البحث بالاسم، رقم الهوية، أو رقم الهاتف...\",\n            \"filter_by_status\": \"تصفية بالحالة\",\n            \"filter_by_category\": \"تصفية بالفئة\",\n            \"all_statuses\": \"جميع الحالات\",\n            \"all_categories\": \"جميع الفئات\",\n            \"no_results_found\": \"لا توجد نتائج مطابقة لمعايير البحث\",\n            \"showing_results\": \"عرض {{count}} من أصل {{total}} مستفيد\",\n            \"export\": \"تصدير\",\n            // Beneficiary Status\n            \"under_review\": \"قيد المراجعة\",\n            \"suspended\": \"معلق\",\n            \"inactive\": \"غير نشط\",\n            // Zakat Categories\n            \"fuqara\": \"الفقراء\",\n            \"masakin\": \"المساكين\",\n            \"amilin\": \"العاملين عليها\",\n            \"muallafah\": \"المؤلفة قلوبهم\",\n            \"riqab\": \"في الرقاب\",\n            \"gharimin\": \"الغارمين\",\n            \"fisabilillah\": \"في سبيل الله\",\n            \"ibnus_sabil\": \"ابن السبيل\",\n            \"primary_category\": \"الفئة الأساسية\",\n            // Table Headers\n            \"name\": \"الاسم\",\n            \"total_received\": \"إجمالي المستلم\",\n            // Actions\n            \"view_profile\": \"عرض الملف الشخصي\",\n            \"manage_case\": \"إدارة الحالة\",\n            \"generate_voucher\": \"إنشاء قسيمة\",\n            \"back\": \"العودة\",\n            // Profile Tabs\n            \"overview\": \"نظرة عامة\",\n            \"personal_details\": \"البيانات الشخصية\",\n            \"eligibility_verification\": \"الأهلية والتحقق\",\n            \"case_management\": \"إدارة الحالة\",\n            \"distribution_history\": \"سجل التوزيعات\",\n            \"family\": \"الأسرة\",\n            // Profile Details\n            \"eligibility_score\": \"نقاط الأهلية\",\n            \"high_score\": \"درجة عالية\",\n            \"family_size\": \"حجم الأسرة\",\n            \"dependents\": \"معالين\",\n            \"last_distribution\": \"آخر توزيع\",\n            \"no_distribution\": \"لا يوجد\",\n            \"not_distributed_yet\": \"لم يتم التوزيع بعد\",\n            // Contact Information\n            \"contact_info\": \"معلومات الاتصال\",\n            \"account_status\": \"حالة الحساب\",\n            \"current_status\": \"الحالة الحالية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"next_review\": \"المراجعة القادمة\",\n            // Personal Information\n            \"personal_information\": \"البيانات الشخصية\",\n            \"basic_information\": \"المعلومات الأساسية\",\n            \"name_arabic\": \"الاسم بالعربية\",\n            \"name_english\": \"الاسم بالإنجليزية\",\n            \"date_of_birth\": \"تاريخ الميلاد\",\n            \"gender\": \"الجنس\",\n            \"male\": \"ذكر\",\n            \"female\": \"أنثى\",\n            // Family Members\n            \"family_members\": \"أفراد الأسرة\",\n            \"no_family_info\": \"لا توجد معلومات عن أفراد الأسرة\",\n            \"dependent\": \"معال\",\n            \"special_needs\": \"احتياجات خاصة\",\n            \"relationship\": \"القرابة\",\n            \"age\": \"العمر\",\n            \"years\": \"سنة\",\n            \"son\": \"ابن\",\n            \"daughter\": \"ابنة\",\n            \"mother\": \"أم\",\n            \"father\": \"أب\",\n            // Documents\n            \"no_documents\": \"لا توجد مستندات مرفوعة\",\n            \"verified\": \"محقق\",\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"upload_date\": \"تاريخ الرفع\",\n            // Coming Soon\n            \"coming_soon\": \"قريباً\",\n            \"chart_coming_soon\": \"الرسم البياني قريباً\",\n            \"under_development\": \"قيد التطوير\",\n            \"registration_form_coming\": \"نموذج التسجيل قيد التطوير\",\n            \"registration_form_desc\": \"سيتم إضافة نموذج تسجيل المستفيدين الجدد قريباً\",\n            \"will_include_features\": \"سيتضمن النموذج الميزات التالية:\",\n            \"multi_step_form\": \"نموذج متعدد الخطوات مع مؤشر التقدم\",\n            \"dual_language_input\": \"إدخال البيانات الشخصية بالعربية والإنجليزية\",\n            \"zakat_categories_selection\": \"اختيار فئات الزكاة الثمانية\",\n            \"document_upload\": \"رفع المستندات المطلوبة\",\n            \"data_validation\": \"التحقق من صحة البيانات\",\n            \"duplicate_detection\": \"كشف التكرار التلقائي\",\n            // System Information\n            \"system_name\": \"نظام إدارة الزكاة\",\n            \"system_description\": \"نظام شامل لإدارة طلبات الزكاة والمساعدات\",\n            // Dashboard\n            \"role\": \"الدور\",\n            \"dashboard_subtitle\": \"نظرة عامة شاملة على النظام\",\n            // Quick Actions Descriptions\n            \"Add a new beneficiary to the system\": \"إضافة مستفيد جديد إلى النظام\",\n            \"Find and manage existing beneficiaries\": \"البحث عن المستفيدين الحاليين وإدارتهم\",\n            \"Create distribution and analytics reports\": \"إنشاء تقارير التوزيع والتحليلات\",\n            \"Configure distribution categories and amounts\": \"تكوين فئات ومبالغ التوزيع\",\n            \"Manage system users and permissions\": \"إدارة مستخدمي النظام والصلاحيات\",\n            \"Configure system preferences\": \"تكوين تفضيلات النظام\",\n            // Recent Activity\n            \"New beneficiary registered\": \"تم تسجيل مستفيد جديد\",\n            \"Application approved\": \"تم الموافقة على الطلب\",\n            \"Zakat distributed\": \"تم توزيع الزكاة\",\n            \"Pending review\": \"في انتظار المراجعة\",\n            \"Ahmed Mohammed Al-Rashid has been registered\": \"تم تسجيل أحمد محمد الراشد\",\n            \"Fatima Al-Zahra application approved for Zakat distribution\": \"تم الموافقة على طلب فاطمة الزهراء لتوزيع الزكاة\",\n            \"5,000 SAR distributed to 10 beneficiaries\": \"تم توزيع 5,000 ريال سعودي على 10 مستفيدين\",\n            \"3 applications require case manager review\": \"3 طلبات تتطلب مراجعة مدير الحالة\",\n            \"Reception Staff\": \"موظف الاستقبال\",\n            \"Case Manager\": \"مدير الحالة\",\n            \"Finance Manager\": \"مدير المالية\",\n            \"System\": \"النظام\",\n            // Authentication Messages\n            \"create_new_account\": \"إنشاء حساب جديد\",\n            \"sign_in_to_account\": \"تسجيل الدخول إلى حسابك\",\n            \"choose_login_method\": \"اختر طريقة تسجيل الدخول المناسبة\",\n            \"enter_details_to_create_account\": \"أدخل بياناتك لإنشاء حساب جديد\",\n            \"confirm_password\": \"تأكيد كلمة المرور\",\n            \"creating_account\": \"جاري إنشاء الحساب...\",\n            \"already_have_account\": \"لديك حساب بالفعل؟\",\n            \"verifying\": \"جاري التحقق...\",\n            \"error\": \"خطأ\",\n            \"passwords_not_match\": \"كلمات المرور غير متطابقة\",\n            \"account_created_success\": \"تم إنشاء الحساب بنجاح\",\n            \"wait_admin_approval\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            \"account_creation_error\": \"خطأ في إنشاء الحساب\",\n            \"unexpected_error\": \"حدث خطأ غير متوقع\",\n            \"error_during_creation\": \"حدث خطأ أثناء إنشاء الحساب\",\n            \"invalid_credentials\": \"البيانات المدخلة غير صحيحة\",\n            \"welcome_to_system\": \"مرحباً بك في نظام إدارة الزكاة\",\n            \"error_during_login\": \"حدث خطأ أثناء تسجيل الدخول\",\n            // Account Status\n            \"account_pending_approval\": \"حسابك في انتظار الموافقة\",\n            \"wait_admin_approval_desc\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            // Dashboard\n            \"assigned_tasks\": \"المهام المخصصة لك\",\n            \"total_requests_desc\": \"إجمالي الطلبات\",\n            \"pending_review_desc\": \"في انتظار المراجعة\",\n            \"approved_today_desc\": \"موافق عليها اليوم\",\n            \"avg_processing_days_desc\": \"متوسط أيام المعالجة\",\n            // Reports\n            \"no_reports_access\": \"ليس لديك صلاحية للوصول إلى التقارير\",\n            \"monthly_report\": \"التقرير الشهري\",\n            \"monthly_stats_desc\": \"إحصائيات الطلبات والموافقات الشهرية\",\n            \"requests_label\": \"الطلبات\",\n            \"approved_label\": \"موافق\",\n            \"rejected_label\": \"مرفوض\",\n            // Requests\n            \"back_button\": \"العودة\",\n            \"request_details\": \"تفاصيل الطلب\",\n            \"download_decision\": \"تحميل القرار\",\n            // Gender and Personal Info\n            \"gender_label\": \"الجنس\",\n            \"male_label\": \"ذكر\",\n            \"female_label\": \"أنثى\",\n            \"marital_status_label\": \"الحالة الاجتماعية\",\n            \"married_label\": \"متزوج\",\n            \"single_label\": \"أعزب\",\n            \"divorced_label\": \"مطلق\",\n            \"widowed_label\": \"أرمل\",\n            // Common UI Text\n            \"or\": \"أو\",\n            \"no_account\": \"ليس لديك حساب؟\",\n            \"demo_accounts\": \"حسابات تجريبية:\",\n            \"applicant\": \"مقدم طلب:\",\n            \"staff_member\": \"موظف:\",\n            // Access Control\n            \"access_denied\": \"غير مصرح\",\n            \"no_beneficiary_access\": \"ليس لديك صلاحية للوصول إلى إدارة المستفيدين\",\n            \"no_registration_access\": \"ليس لديك صلاحية لتسجيل مستفيدين جدد\",\n            // Multi-Step Form\n            \"step\": \"خطوة\",\n            \"of\": \"من\",\n            \"complete\": \"مكتمل\",\n            \"optional\": \"اختياري\",\n            \"previous\": \"السابق\",\n            \"next\": \"التالي\",\n            \"save_draft\": \"حفظ مسودة\",\n            \"submit\": \"إرسال\",\n            // Form Steps\n            \"personal_details_step\": \"البيانات الشخصية\",\n            \"contact_information_step\": \"معلومات الاتصال\",\n            \"eligibility_criteria_step\": \"معايير الأهلية\",\n            \"documentation_upload_step\": \"رفع المستندات\",\n            \"review_submit_step\": \"مراجعة وإرسال\",\n            // Form Validation\n            \"field_required\": \"هذا الحقل مطلوب\",\n            \"invalid_email\": \"عنوان البريد الإلكتروني غير صحيح\",\n            \"invalid_phone\": \"رقم الهاتف غير صحيح\",\n            \"invalid_national_id\": \"رقم الهوية الوطنية غير صحيح\",\n            \"min_length\": \"يجب أن يكون الحد الأدنى {{min}} أحرف\",\n            \"max_length\": \"يجب أن لا يتجاوز {{max}} حرف\",\n            \"invalid_date\": \"التاريخ غير صحيح\",\n            \"future_date_not_allowed\": \"لا يمكن أن يكون التاريخ في المستقبل\",\n            \"invalid_arabic_name\": \"يجب أن يحتوي الاسم على أحرف عربية فقط\",\n            \"invalid_english_name\": \"يجب أن يحتوي الاسم على أحرف إنجليزية فقط\",\n            \"invalid_age\": \"العمر غير صحيح\",\n            \"invalid_postal_code\": \"الرمز البريدي غير صحيح\",\n            \"select_at_least_one_category\": \"يجب اختيار فئة واحدة على الأقل\",\n            \"min_family_size\": \"حجم الأسرة يجب أن يكون 1 على الأقل\",\n            \"max_family_size\": \"حجم الأسرة لا يمكن أن يتجاوز 20\",\n            \"min_dependents\": \"عدد المعالين لا يمكن أن يكون سالباً\",\n            \"max_dependents\": \"عدد المعالين لا يمكن أن يتجاوز 19\",\n            \"invalid_income\": \"الدخل الشهري غير صحيح\",\n            \"max_income\": \"الدخل الشهري لا يمكن أن يتجاوز 50,000 ريال\",\n            \"upload_at_least_one_document\": \"يجب رفع مستند واحد على الأقل\",\n            \"national_id_required\": \"رفع صورة الهوية الوطنية مطلوب\",\n            \"terms_must_be_accepted\": \"يجب الموافقة على الشروط والأحكام\",\n            \"data_accuracy_must_be_confirmed\": \"يجب تأكيد صحة البيانات\",\n            \"privacy_policy_must_be_accepted\": \"يجب الموافقة على سياسة الخصوصية\",\n            // Personal Details Form\n            \"personal_details_description\": \"أدخل المعلومات الشخصية الأساسية للمستفيد\",\n            \"enter_name_arabic\": \"أدخل الاسم الكامل بالعربية\",\n            \"enter_name_english\": \"أدخل الاسم الكامل بالإنجليزية\",\n            \"name_arabic_description\": \"الاسم كما هو مكتوب في الهوية الوطنية\",\n            \"name_english_description\": \"الاسم بالأحرف الإنجليزية\",\n            \"enter_national_id\": \"أدخل رقم الهوية الوطنية\",\n            \"national_id_description\": \"رقم الهوية الوطنية السعودية (10 أرقام)\",\n            \"pick_date\": \"اختر التاريخ\",\n            \"date_of_birth_description\": \"تاريخ الميلاد كما هو مكتوب في الهوية\",\n            \"select_gender\": \"اختر الجنس\",\n            \"select_marital_status\": \"اختر الحالة الاجتماعية\",\n            // Contact Information Form\n            \"contact_information_description\": \"أدخل معلومات الاتصال والعنوان\",\n            \"enter_phone_number\": \"أدخل رقم الهاتف\",\n            \"phone_number_description\": \"رقم الهاتف المحمول (يبدأ بـ 05 أو +966)\",\n            \"enter_email\": \"أدخل البريد الإلكتروني\",\n            \"email_description\": \"البريد الإلكتروني للتواصل (اختياري)\",\n            \"enter_address\": \"أدخل العنوان التفصيلي\",\n            \"address_description\": \"العنوان الكامل بما في ذلك الحي والشارع\",\n            \"enter_city\": \"أدخل المدينة\",\n            \"city_description\": \"المدينة أو المحافظة\",\n            \"select_region\": \"اختر المنطقة\",\n            \"region_description\": \"المنطقة الإدارية في المملكة\",\n            \"enter_postal_code\": \"أدخل الرمز البريدي\",\n            \"postal_code_description\": \"الرمز البريدي (5 أرقام)\",\n            // Eligibility Criteria Form\n            \"islamic_compliance_notice\": \"إشعار الامتثال الإسلامي\",\n            \"zakat_categories_description\": \"يتم تصنيف المستفيدين وفقاً للفئات الثمانية للزكاة المحددة في الشريعة الإسلامية\",\n            \"select_applicable_categories\": \"اختر الفئات المناسبة للمستفيد\",\n            \"applicable_categories\": \"الفئات المناسبة\",\n            \"select_primary_category\": \"اختر الفئة الأساسية\",\n            \"primary_category_description\": \"الفئة الأساسية التي ينتمي إليها المستفيد\",\n            \"family_financial_info\": \"المعلومات الأسرية والمالية\",\n            \"family_financial_description\": \"معلومات حول حجم الأسرة والوضع المالي\",\n            \"family_size_description\": \"العدد الإجمالي لأفراد الأسرة\",\n            \"dependents_description\": \"عدد الأشخاص المعالين\",\n            \"monthly_income_description\": \"الدخل الشهري الإجمالي بالريال السعودي\",\n            \"has_special_needs\": \"يوجد احتياجات خاصة\",\n            \"special_needs_description\": \"هل يوجد أحد من أفراد الأسرة لديه احتياجات خاصة؟\",\n            \"special_needs_details\": \"تفاصيل الاحتياجات الخاصة\",\n            \"describe_special_needs\": \"اوصف الاحتياجات الخاصة بالتفصيل\",\n            \"special_needs_details_description\": \"وصف مفصل للاحتياجات الخاصة أو الظروف الصحية\",\n            // Zakat Category Descriptions\n            \"fuqara_description\": \"الفقراء - الذين لا يملكون ما يكفي لسد حاجاتهم الأساسية\",\n            \"masakin_description\": \"المساكين - الذين يملكون أقل من نصف كفايتهم\",\n            \"amilin_description\": \"العاملين عليها - الذين يعملون في جمع وتوزيع الزكاة\",\n            \"muallafah_description\": \"المؤلفة قلوبهم - الذين يُراد تأليف قلوبهم للإسلام\",\n            \"riqab_description\": \"في الرقاب - لتحرير العبيد والأسرى\",\n            \"gharimin_description\": \"الغارمين - المدينين الذين لا يستطيعون سداد ديونهم\",\n            \"fisabilillah_description\": \"في سبيل الله - للجهاد والأعمال الخيرية\",\n            \"ibnus_sabil_description\": \"ابن السبيل - المسافر المنقطع عن بلده\",\n            // Documentation Upload Form\n            \"document_requirements\": \"متطلبات المستندات\",\n            \"document_requirements_description\": \"يرجى رفع المستندات المطلوبة لإتمام عملية التسجيل\",\n            \"required_document_missing\": \"مستند مطلوب مفقود\",\n            \"national_id_document_required\": \"رفع صورة الهوية الوطنية مطلوب لإتمام التسجيل\",\n            \"upload_documents\": \"رفع المستندات\",\n            \"upload_documents_description\": \"اسحب وأفلت الملفات هنا أو انقر للتصفح\",\n            \"drag_drop_files\": \"اسحب وأفلت الملفات هنا\",\n            \"drop_files_here\": \"أفلت الملفات هنا\",\n            \"or_click_to_browse\": \"أو انقر للتصفح\",\n            \"supported_formats\": \"الصيغ المدعومة\",\n            \"uploaded_documents\": \"المستندات المرفوعة\",\n            \"uploading\": \"جاري الرفع\",\n            \"required\": \"مطلوب\",\n            \"national_id_document\": \"صورة الهوية الوطنية\",\n            \"income_certificate\": \"شهادة الدخل\",\n            \"family_card\": \"كرت العائلة\",\n            \"medical_report\": \"التقرير الطبي\",\n            \"other_document\": \"مستند آخر\",\n            \"additional_notes\": \"ملاحظات إضافية\",\n            \"additional_notes_description\": \"أي معلومات إضافية تود إضافتها\",\n            \"enter_additional_notes\": \"أدخل أي ملاحظات إضافية\",\n            \"additional_notes_help\": \"معلومات إضافية قد تساعد في تقييم الطلب\",\n            // Review and Submit Form\n            \"review_information\": \"مراجعة المعلومات\",\n            \"review_information_description\": \"يرجى مراجعة جميع المعلومات المدخلة قبل الإرسال\",\n            \"not_provided\": \"غير مقدم\",\n            \"no_documents_uploaded\": \"لم يتم رفع أي مستندات\",\n            \"terms_and_conditions\": \"الشروط والأحكام\",\n            \"terms_conditions_description\": \"يرجى قراءة والموافقة على الشروط والأحكام\",\n            \"accept_terms_conditions\": \"أوافق على الشروط والأحكام\",\n            \"terms_conditions_text\": \"لقد قرأت وفهمت وأوافق على شروط وأحكام نظام إدارة الزكاة\",\n            \"confirm_data_accuracy\": \"أؤكد صحة البيانات\",\n            \"data_accuracy_text\": \"أؤكد أن جميع المعلومات المقدمة صحيحة ودقيقة\",\n            \"accept_privacy_policy\": \"أوافق على سياسة الخصوصية\",\n            \"privacy_policy_text\": \"أوافق على سياسة الخصوصية وطريقة التعامل مع البيانات الشخصية\",\n            // Enhanced Validation Messages\n            \"min_value\": \"يجب أن تكون القيمة {{min}} على الأقل\",\n            \"max_value\": \"يجب أن لا تتجاوز القيمة {{max}}\",\n            \"validation_error\": \"خطأ في التحقق من صحة البيانات\",\n            \"age_below_18_warning\": \"العمر أقل من 18 سنة - قد يتطلب موافقة ولي الأمر\",\n            \"age_above_100_warning\": \"العمر أكبر من 100 سنة - يرجى التحقق من التاريخ\",\n            \"dependents_family_size_warning\": \"عدد المعالين يجب أن يكون أقل من حجم الأسرة\",\n            \"high_income_warning\": \"الدخل مرتفع - قد لا يكون مؤهلاً للزكاة\",\n            \"duplicate_beneficiary_found\": \"تم العثور على مستفيد مشابه في النظام\",\n            \"duplicate_national_id\": \"رقم الهوية الوطنية مسجل مسبقاً\",\n            \"duplicate_phone\": \"رقم الهاتف مسجل مسبقاً\",\n            \"duplicate_email\": \"البريد الإلكتروني مسجل مسبقاً\",\n            \"similar_name_found\": \"تم العثور على اسم مشابه في النظام\",\n            // Accessibility Messages\n            \"form_has_error\": \"النموذج يحتوي على خطأ واحد\",\n            \"form_has_errors\": \"النموذج يحتوي على {{count}} أخطاء\",\n            \"step_announcement\": \"الخطوة {{current}} من {{total}}: {{title}}\",\n            \"progress_announcement\": \"تم إكمال {{percentage}}% من النموذج\",\n            \"required_field\": \"حقل مطلوب\",\n            \"optional_field\": \"حقل اختياري\",\n            \"error_in_field\": \"خطأ في الحقل\",\n            \"field_description\": \"وصف الحقل\",\n            \"form_navigation\": \"التنقل في النموذج\",\n            \"skip_to_content\": \"تخطي إلى المحتوى\",\n            \"skip_to_navigation\": \"تخطي إلى التنقل\",\n            // Final Implementation Messages\n            \"draft_saved_successfully\": \"تم حفظ المسودة بنجاح\",\n            \"error_saving_draft\": \"خطأ في حفظ المسودة\",\n            \"duplicate_check_failed\": \"فشل في فحص التكرار\",\n            \"beneficiary_registered_successfully\": \"تم تسجيل المستفيد بنجاح\",\n            \"beneficiary_registration_success_description\": \"تم إضافة المستفيد إلى النظام وسيتم مراجعة الطلب\",\n            \"error_submitting_form\": \"خطأ في إرسال النموذج\",\n            \"draft_loaded\": \"تم تحميل المسودة المحفوظة\",\n            \"submitting_registration\": \"جاري إرسال التسجيل\",\n            \"please_wait\": \"يرجى الانتظار...\",\n            \"beneficiary_registration_description\": \"قم بتسجيل مستفيد جديد في نظام إدارة الزكاة\",\n            // Assistance Types Management\n            \"assistance_types_management\": \"إدارة أنواع المساعدات\",\n            \"assistance_types_management_desc\": \"تكوين أنواع المساعدات ومعايير الأهلية والمتطلبات\",\n            \"add_new_type\": \"إضافة نوع جديد\",\n            \"create_assistance_type\": \"إنشاء نوع مساعدة\",\n            \"edit_assistance_type\": \"تعديل نوع المساعدة\",\n            \"assistance_type_details\": \"تفاصيل نوع المساعدة\",\n            \"basic_info_section\": \"المعلومات الأساسية\",\n            \"configure_basic_details\": \"تكوين التفاصيل الأساسية لنوع المساعدة\",\n            \"assistance_name_arabic\": \"الاسم بالعربية\",\n            \"assistance_name_english\": \"الاسم بالإنجليزية\",\n            \"description_arabic\": \"الوصف بالعربية\",\n            \"description_english\": \"الوصف بالإنجليزية\",\n            \"maximum_amount\": \"الحد الأقصى للمبلغ\",\n            \"maximum_amount_sar\": \"الحد الأقصى للمبلغ (ريال سعودي)\",\n            \"category\": \"الفئة\",\n            \"active_status\": \"الحالة النشطة\",\n            \"enable_assistance_type\": \"تفعيل نوع المساعدة للطلبات\",\n            \"required_documents\": \"المستندات المطلوبة\",\n            \"configure_required_documents\": \"تكوين المستندات المطلوبة لنوع المساعدة\",\n            \"add_document\": \"إضافة مستند\",\n            \"no_documents_configured\": \"لم يتم تكوين أي مستندات بعد\",\n            \"click_add_document\": 'انقر على \"إضافة مستند\" للبدء',\n            \"document_name_arabic\": \"اسم المستند بالعربية\",\n            \"document_name_english\": \"اسم المستند بالإنجليزية\",\n            \"is_required\": \"مطلوب\",\n            \"max_size_kb\": \"الحد الأقصى للحجم (كيلوبايت)\",\n            \"accepted_formats\": \"الصيغ المقبولة\",\n            \"total_types\": \"إجمالي الأنواع\",\n            \"active_types\": \"الأنواع النشطة\",\n            \"inactive_types\": \"الأنواع غير النشطة\",\n            \"max_amount\": \"أقصى مبلغ\",\n            \"showing_types\": \"عرض {{count}} من {{total}} أنواع المساعدات\",\n            \"no_assistance_types\": \"لا توجد أنواع مساعدات\",\n            \"search_assistance_types\": \"البحث في أنواع المساعدات...\",\n            \"view_details\": \"عرض التفاصيل\",\n            \"activate\": \"تفعيل\",\n            \"deactivate\": \"إلغاء التفعيل\",\n            \"assistance_type_not_found\": \"نوع المساعدة غير موجود\",\n            \"assistance_type_not_found_desc\": \"لم يتم العثور على نوع المساعدة المطلوب\",\n            \"back_to_assistance_types\": \"العودة إلى أنواع المساعدات\",\n            \"create_new_assistance_type\": \"إنشاء نوع مساعدة جديد\",\n            \"configure_assistance_type\": \"تكوين نوع مساعدة جديد مع معايير الأهلية والمتطلبات\",\n            \"back_to_details\": \"العودة إلى التفاصيل\",\n            \"modify_assistance_type\": \"تعديل تكوين نوع المساعدة\",\n            \"update_assistance_type\": \"تحديث نوع المساعدة\",\n            \"assistance_type_created\": \"تم إنشاء نوع المساعدة بنجاح\",\n            \"assistance_type_updated\": \"تم تحديث نوع المساعدة بنجاح\",\n            \"failed_create_assistance_type\": \"فشل في إنشاء نوع المساعدة\",\n            \"failed_update_assistance_type\": \"فشل في تحديث نوع المساعدة\",\n            \"cancel\": \"إلغاء\",\n            \"create\": \"إنشاء\",\n            \"update\": \"تحديث\",\n            \"saving\": \"جاري الحفظ...\",\n            \"eligibility_criteria\": \"معايير الأهلية\",\n            \"usage_statistics\": \"إحصائيات الاستخدام\",\n            \"total_applications\": \"إجمالي الطلبات\",\n            \"approved\": \"موافق عليها\",\n            \"rejected\": \"مرفوضة\",\n            // Common Admin Terms\n            \"manage\": \"إدارة\",\n            \"configure\": \"تكوين\",\n            \"actions\": \"الإجراءات\",\n            \"details\": \"التفاصيل\",\n            \"admin_overview\": \"نظرة عامة\",\n            \"admin_statistics\": \"الإحصائيات\",\n            \"all_operational\": \"جميعها تعمل\",\n            \"good\": \"جيد\",\n            \"all_systems_operational\": \"جميع الأنظمة تعمل\",\n            \"recently_updated\": \"محدثة مؤخراً\",\n            \"from_last_month\": \"من الشهر الماضي\",\n            \"documents_count\": \"{{count}} مستندات\",\n            \"uses\": \"استخدامات\",\n            \"formats\": \"الصيغ\",\n            \"size\": \"الحجم\",\n            \"mb\": \"ميجابايت\",\n            \"status_active\": \"نشط\",\n            \"status_inactive\": \"غير نشط\",\n            \"configure_requirements\": \"تكوين المتطلبات\",\n            \"advanced_filters\": \"مرشحات متقدمة\",\n            \"most_used\": \"الأكثر استخداماً\",\n            \"total_usage\": \"إجمالي الاستخدام\",\n            // User Management\n            \"manage_user_accounts_roles_permissions\": \"إدارة حسابات المستخدمين والأدوار والصلاحيات\",\n            \"add_new_user\": \"إضافة مستخدم جديد\",\n            \"search_users_placeholder\": \"البحث في المستخدمين بالاسم أو البريد الإلكتروني أو الدور...\",\n            \"active_users\": \"المستخدمون النشطون\",\n            \"pending_approval\": \"في انتظار الموافقة\",\n            \"admin_users\": \"مستخدمو الإدارة\",\n            \"system_users\": \"مستخدمو النظام\",\n            \"showing_users\": \"عرض {{count}} من أصل {{total}} مستخدم\",\n            \"user\": \"المستخدم\",\n            \"last_login\": \"آخر تسجيل دخول\",\n            \"created\": \"تاريخ الإنشاء\",\n            \"no_users_found\": \"لم يتم العثور على مستخدمين\",\n            \"never\": \"أبداً\",\n            \"edit_user\": \"تعديل المستخدم\",\n            \"reset_password\": \"إعادة تعيين كلمة المرور\",\n            \"change_role\": \"تغيير الدور\",\n            \"suspend_user\": \"تعليق المستخدم\",\n            \"activate_user\": \"تفعيل المستخدم\",\n            // Status values\n            \"active\": \"نشط\",\n            // Workflow Management\n            \"configure_approval_workflows_business_rules\": \"تكوين سير عمل الموافقات وقواعد العمل لمعالجة الطلبات\",\n            \"create_workflow\": \"إنشاء سير عمل\",\n            \"active_workflows\": \"سير العمل النشط\",\n            \"avg_steps\": \"متوسط الخطوات\",\n            \"active_roles\": \"الأدوار النشطة\",\n            \"workflows\": \"سير العمل\",\n            \"role_configuration\": \"تكوين الأدوار\",\n            \"business_rules\": \"قواعد العمل\",\n            \"testing_simulation\": \"الاختبار والمحاكاة\",\n            \"financial_support_workflow\": \"سير عمل الدعم المالي\",\n            \"standard_workflow_financial_assistance\": \"سير العمل القياسي لطلبات المساعدة المالية\",\n            \"medical_assistance_workflow\": \"سير عمل المساعدة الطبية\",\n            \"specialized_workflow_medical_aid\": \"سير العمل المتخصص لطلبات المساعدة الطبية\",\n            \"emergency_relief_workflow\": \"سير عمل الإغاثة الطارئة\",\n            \"fast_track_workflow_emergency\": \"سير العمل السريع للحالات الطارئة\",\n            \"steps\": \"خطوات\",\n            \"requests_processed\": \"طلبات معالجة\",\n            \"test\": \"اختبار\",\n            // Document Configuration\n            \"manage_document_types_requirements\": \"إدارة أنواع المستندات ومتطلبات طلبات المساعدة\",\n            \"add_document_type\": \"إضافة نوع مستند\",\n            \"search_document_types_placeholder\": \"البحث في أنواع المستندات...\",\n            \"document_types\": \"أنواع المستندات\",\n            \"requirements_mapping\": \"ربط المتطلبات\",\n            \"validation_rules\": \"قواعد التحقق\",\n            \"showing_document_types\": \"عرض {{count}} من أصل {{total}} من أنواع المستندات\",\n            \"max_size\": \"الحد الأقصى للحجم\",\n            \"usage_count\": \"عدد الاستخدامات\",\n            \"no_document_types_found\": \"لم يتم العثور على أنواع مستندات\",\n            \"document_requirements_mapping\": \"ربط متطلبات المستندات\",\n            \"configure_document_requirements_per_assistance_type\": \"تكوين متطلبات المستندات لكل نوع مساعدة\",\n            \"requirements_mapping_interface\": \"واجهة ربط المتطلبات\",\n            \"configure_document_requirements_per_assistance_type_desc\": \"تكوين متطلبات المستندات لكل نوع مساعدة\",\n            \"validation_rules_title\": \"قواعد التحقق\",\n            \"configure_file_format_size_validation\": \"تكوين قواعد التحقق من تنسيق الملف والحجم\",\n            \"validation_rules_interface\": \"واجهة قواعد التحقق\",\n            \"configure_file_validation_parameters\": \"تكوين معاملات التحقق من الملف\",\n            // System Settings\n            \"configure_general_system_parameters\": \"تكوين معاملات النظام العامة والإعدادات التشغيلية\",\n            \"save_changes\": \"حفظ التغييرات\",\n            \"general\": \"عام\",\n            \"calculation\": \"الحساب\",\n            \"notifications\": \"الإشعارات\",\n            \"limits\": \"الحدود\",\n            \"backup\": \"النسخ الاحتياطي\",\n            \"organization_information\": \"معلومات المنظمة\",\n            \"basic_organization_contact_info\": \"تفاصيل المنظمة الأساسية ومعلومات الاتصال\",\n            \"organization_name_english\": \"اسم المنظمة (بالإنجليزية)\",\n            \"organization_name_arabic\": \"اسم المنظمة (بالعربية)\",\n            \"contact_email\": \"البريد الإلكتروني للاتصال\",\n            \"contact_phone\": \"هاتف الاتصال\",\n            \"address\": \"العنوان\",\n            \"zakat_calculation_parameters\": \"معاملات حساب الزكاة\",\n            \"configure_nisab_calculation_methods\": \"تكوين قيم النصاب وطرق الحساب\",\n            \"nisab_gold_grams\": \"نصاب الذهب (جرام)\",\n            \"nisab_silver_grams\": \"نصاب الفضة (جرام)\",\n            \"zakat_rate_percentage\": \"معدل الزكاة (%)\",\n            \"use_hijri_calendar\": \"استخدام التقويم الهجري\",\n            \"use_islamic_calendar_zakat_calculations\": \"استخدام التقويم الإسلامي لحسابات الزكاة\",\n            \"notification_preferences\": \"تفضيلات الإشعارات\",\n            \"configure_system_notifications_alerts\": \"تكوين إشعارات النظام والتنبيهات\",\n            \"email_notifications\": \"إشعارات البريد الإلكتروني\",\n            \"send_notifications_via_email\": \"إرسال الإشعارات عبر البريد الإلكتروني\",\n            \"sms_notifications\": \"إشعارات الرسائل النصية\",\n            \"send_notifications_via_sms\": \"إرسال الإشعارات عبر الرسائل النصية\",\n            \"system_alerts\": \"تنبيهات النظام\",\n            \"show_in_app_system_alerts\": \"عرض تنبيهات النظام داخل التطبيق\",\n            \"system_limits_thresholds\": \"حدود النظام والعتبات\",\n            \"configure_operational_limits_security\": \"تكوين الحدود التشغيلية ومعاملات الأمان\",\n            \"max_request_amount_sar\": \"الحد الأقصى لمبلغ الطلب (ريال سعودي)\",\n            \"session_timeout_minutes\": \"انتهاء مهلة الجلسة (دقائق)\",\n            \"max_file_size_mb\": \"الحد الأقصى لحجم الملف (ميجابايت)\",\n            \"backup_maintenance\": \"النسخ الاحتياطي والصيانة\",\n            \"configure_backup_data_retention\": \"تكوين إعدادات النسخ الاحتياطي وسياسات الاحتفاظ بالبيانات\",\n            \"automatic_backup\": \"النسخ الاحتياطي التلقائي\",\n            \"enable_automatic_system_backups\": \"تفعيل النسخ الاحتياطي التلقائي للنظام\",\n            \"backup_frequency\": \"تكرار النسخ الاحتياطي\",\n            \"hourly\": \"كل ساعة\",\n            \"daily\": \"يومياً\",\n            \"weekly\": \"أسبوعياً\",\n            \"monthly\": \"شهرياً\",\n            \"retention_period_days\": \"فترة الاحتفاظ (أيام)\",\n            \"success\": \"نجح\",\n            \"system_settings_saved_successfully\": \"تم حفظ إعدادات النظام بنجاح\",\n            \"failed_to_save_settings\": \"فشل في حفظ الإعدادات\",\n            // Audit Trail\n            \"view_system_activity_logs_compliance\": \"عرض سجلات نشاط النظام ومسارات المراجعة لمراقبة الامتثال\",\n            \"export_logs\": \"تصدير السجلات\",\n            \"search_audit_logs_placeholder\": \"البحث في سجلات المراجعة بالمستخدم أو الإجراء أو الكيان...\",\n            \"total_events\": \"إجمالي الأحداث\",\n            \"security_events\": \"أحداث الأمان\",\n            \"warnings\": \"التحذيرات\",\n            \"todays_events\": \"أحداث اليوم\",\n            \"system_activity_logs\": \"سجلات نشاط النظام\",\n            \"showing_audit_events\": \"عرض {{count}} من أصل {{total}} من أحداث المراجعة\",\n            \"timestamp\": \"الطابع الزمني\",\n            \"action\": \"الإجراء\",\n            \"entity\": \"الكيان\",\n            \"severity\": \"الخطورة\",\n            \"ip_address\": \"عنوان IP\",\n            \"no_audit_logs_found\": \"لم يتم العثور على سجلات مراجعة\",\n            \"common_audit_operations_reports\": \"عمليات مراجعة الحسابات الشائعة والتقارير\",\n            \"security_report\": \"تقرير الأمان\",\n            \"generate_security_events_report\": \"إنشاء تقرير أحداث الأمان\",\n            \"user_activity_report\": \"تقرير نشاط المستخدم\",\n            \"export_user_activity_summary\": \"تصدير ملخص نشاط المستخدم\",\n            \"compliance_report\": \"تقرير الامتثال\",\n            \"generate_compliance_audit_report\": \"إنشاء تقرير مراجعة الامتثال\"\n        }\n    },\n    en: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"Dashboard\",\n            \"profile\": \"Profile\",\n            \"requests\": \"Requests\",\n            \"tasks\": \"Tasks\",\n            \"reports\": \"Reports\",\n            \"settings\": \"Settings\",\n            \"logout\": \"Logout\",\n            // Authentication\n            \"login\": \"Login\",\n            \"register\": \"Register\",\n            \"email\": \"Email\",\n            \"password\": \"Password\",\n            \"full_name\": \"Full Name\",\n            \"national_id\": \"National ID\",\n            \"phone_number\": \"Phone Number\",\n            \"login_tawtheeq\": \"Login with Tawtheeq\",\n            \"login_success\": \"Login successful\",\n            \"login_error\": \"Invalid credentials\",\n            // Dashboard Enhancements\n            \"welcome\": \"Welcome\",\n            \"overview_status\": \"Overview of\",\n            \"system_overview\": \"system status\",\n            \"your_requests\": \"your requests\",\n            \"total_beneficiaries\": \"Total Beneficiaries\",\n            \"total_zakat_distributed\": \"Total Zakat Distributed\",\n            \"pending_applications\": \"Pending Applications\",\n            \"active_distributions\": \"Active Distributions\",\n            \"quick_actions\": \"Quick Actions\",\n            \"register_new_beneficiary\": \"Register New Beneficiary\",\n            \"search_beneficiaries\": \"Search Beneficiaries\",\n            \"generate_report\": \"Generate Report\",\n            \"manage_distributions\": \"Manage Distributions\",\n            \"recent_activity\": \"Recent Activity\",\n            \"distribution_overview\": \"Distribution Overview\",\n            \"monthly_distributions\": \"Monthly Distributions\",\n            \"beneficiary_categories\": \"Beneficiary Categories\",\n            \"view_all\": \"View All\",\n            \"this_month\": \"This Month\",\n            \"last_month\": \"Last Month\",\n            \"last_week\": \"Last Week\",\n            \"this_week\": \"This Week\",\n            \"increase\": \"increase\",\n            \"decrease\": \"decrease\",\n            \"from\": \"from\",\n            \"sar\": \"SAR\",\n            \"beneficiaries\": \"Beneficiaries\",\n            \"applications\": \"applications\",\n            \"distributions\": \"distributions\",\n            \"completed\": \"Completed\",\n            \"pending\": \"Pending\",\n            \"warning\": \"Warning\",\n            // Profile\n            \"personal_profile\": \"Personal Profile\",\n            \"basic_info\": \"Basic Information\",\n            \"family_info\": \"Family Information\",\n            \"employment_info\": \"Employment Information\",\n            \"financial_info\": \"Financial Information\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Assistance Requests\n            \"assistance_request\": \"Assistance Request\",\n            \"new_request\": \"New Request\",\n            \"request_type\": \"Request Type\",\n            \"requested_amount\": \"Requested Amount\",\n            \"approved_amount\": \"Approved Amount\",\n            \"description\": \"Description\",\n            \"attach_documents\": \"Attach Documents\",\n            \"submit_request\": \"Submit Request\",\n            \"requests_subtitle_applicant\": \"Manage your assistance requests\",\n            \"requests_subtitle_reviewer\": \"Review all assistance requests\",\n            \"search_requests_placeholder\": \"Search requests...\",\n            \"request_number\": \"Request Number\",\n            \"submission_date\": \"Submission Date\",\n            \"processing_stages\": \"Processing Stages\",\n            \"no_requests\": \"No requests\",\n            \"no_requests_search\": \"No requests found matching search criteria\",\n            \"no_requests_applicant\": \"You have not submitted any requests yet\",\n            \"no_requests_reviewer\": \"No requests to review\",\n            \"submit_new_request\": \"Submit New Request\",\n            // Status\n            \"draft\": \"Draft\",\n            \"submitted\": \"Submitted\",\n            // Workflow Stages\n            \"reception_review\": \"Reception Review\",\n            \"researcher_review\": \"Researcher Review\",\n            \"banking_expert_review\": \"Banking Expert Review\",\n            \"department_head_review\": \"Department Head Review\",\n            \"admin_manager_review\": \"Admin Manager Review\",\n            \"minister_review\": \"Minister Review\",\n            // Actions\n            \"approve\": \"Approve\",\n            \"reject\": \"Reject\",\n            \"return\": \"Return\",\n            \"save\": \"Save\",\n            \"edit\": \"Edit\",\n            \"delete\": \"Delete\",\n            \"view\": \"View\",\n            \"download\": \"Download\",\n            \"upload\": \"Upload\",\n            // Common\n            \"loading\": \"Loading...\",\n            \"search\": \"Search\",\n            \"filter\": \"Filter\",\n            \"date\": \"Date\",\n            \"amount\": \"Amount\",\n            \"status\": \"Status\",\n            \"notes\": \"Notes\",\n            \"documents\": \"Documents\",\n            \"history\": \"History\",\n            // Dashboard Stats\n            \"total_requests\": \"Total Requests\",\n            \"pending_review\": \"Pending Review\",\n            \"approved_today\": \"Approved Today\",\n            \"rejected_today\": \"Rejected Today\",\n            \"average_processing_days\": \"Avg. Processing Days\",\n            \"total_users\": \"Total Users\",\n            // User Roles\n            \"zakat_applicant\": \"Zakat Applicant\",\n            \"reception_staff\": \"Reception Staff\",\n            \"researcher\": \"Researcher\",\n            \"banking_expert\": \"Banking Expert\",\n            \"department_head\": \"Department Head\",\n            \"admin_manager\": \"Administration Manager\",\n            \"minister\": \"Minister\",\n            \"system_admin\": \"System Administrator\",\n            // Admin Navigation\n            \"User Management\": \"User Management\",\n            \"Assistance Types\": \"Assistance Types\",\n            \"System Settings\": \"System Settings\",\n            // Admin System\n            \"admin_dashboard\": \"Admin Dashboard\",\n            \"system_administration\": \"System Administration\",\n            \"admin_access_required\": \"Admin access required\",\n            \"authentication_required\": \"Authentication required\",\n            \"please_login\": \"Please login\",\n            \"back_to_dashboard\": \"Back to Dashboard\",\n            // Admin Navigation\n            \"admin_dashboard_nav\": \"Admin Dashboard\",\n            \"user_management\": \"User Management\",\n            \"assistance_types\": \"Assistance Types\",\n            \"workflow_management\": \"Workflow Management\",\n            \"document_configuration\": \"Document Configuration\",\n            \"distribution_rules\": \"Distribution Rules\",\n            \"system_settings\": \"System Settings\",\n            \"audit_trail\": \"Audit Trail\",\n            \"data_management\": \"Data Management\",\n            // Admin Descriptions\n            \"system_administration_overview\": \"System administration overview\",\n            \"manage_user_accounts_roles\": \"Manage user accounts and roles\",\n            \"configure_aid_types_eligibility\": \"Configure aid types and eligibility\",\n            \"configure_approval_workflows\": \"Configure approval workflows\",\n            \"manage_document_requirements\": \"Manage document requirements\",\n            \"configure_application_distribution\": \"Configure application distribution\",\n            \"general_system_configuration\": \"General system configuration\",\n            \"view_system_activity_logs\": \"View system activity logs\",\n            \"import_export_backup\": \"Import/export and backup\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"Beneficiary Management\",\n            \"beneficiary_management_desc\": \"Manage and track Zakat and assistance beneficiaries\",\n            \"beneficiary_registration\": \"New Beneficiary Registration\",\n            \"beneficiary_registration_desc\": \"Add a new beneficiary to the Zakat management system\",\n            \"beneficiary_list\": \"Beneficiary List\",\n            \"beneficiary_profile\": \"Beneficiary Profile\",\n            \"beneficiary_details\": \"Beneficiary Details\",\n            \"beneficiary_not_found\": \"Beneficiary Not Found\",\n            \"beneficiary_not_found_desc\": \"The requested beneficiary could not be found\",\n            \"back_to_beneficiaries\": \"Back to Beneficiaries List\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"Approved\",\n            \"under_review_beneficiaries\": \"Under Review\",\n            \"total_distributions\": \"Total Distributions\",\n            \"average_distribution\": \"Average Distribution\",\n            \"pending_verification_count\": \"Pending Verification\",\n            \"needs_review\": \"Needs Review\",\n            \"of_total\": \"of Total\",\n            \"per_beneficiary\": \"per Beneficiary\",\n            // Search and Filters\n            \"search_and_filter\": \"Search and Filter\",\n            \"search_placeholder\": \"Search by name, national ID, or phone number...\",\n            \"filter_by_status\": \"Filter by Status\",\n            \"filter_by_category\": \"Filter by Category\",\n            \"all_statuses\": \"All Statuses\",\n            \"all_categories\": \"All Categories\",\n            \"no_results_found\": \"No results found matching search criteria\",\n            \"showing_results\": \"Showing {{count}} of {{total}} beneficiaries\",\n            \"export\": \"Export\",\n            // Beneficiary Status\n            \"pending_verification\": \"Pending Verification\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"suspended\": \"Suspended\",\n            \"inactive\": \"Inactive\",\n            // Zakat Categories\n            \"fuqara\": \"The Poor\",\n            \"masakin\": \"The Needy\",\n            \"amilin\": \"Zakat Administrators\",\n            \"muallafah\": \"Those whose hearts are reconciled\",\n            \"riqab\": \"To free slaves/captives\",\n            \"gharimin\": \"Those in debt\",\n            \"fisabilillah\": \"In the cause of Allah\",\n            \"ibnus_sabil\": \"The wayfarer/traveler\",\n            \"primary_category\": \"Primary Category\",\n            // Table Headers\n            \"name\": \"Name\",\n            \"total_received\": \"Total Received\",\n            // Actions\n            \"view_profile\": \"View Profile\",\n            \"manage_case\": \"Manage Case\",\n            \"generate_voucher\": \"Generate Voucher\",\n            \"back\": \"Back\",\n            // Profile Tabs\n            \"overview\": \"Overview\",\n            \"personal_details\": \"Personal Details\",\n            \"eligibility_verification\": \"Eligibility & Verification\",\n            \"case_management\": \"Case Management\",\n            \"distribution_history\": \"Distribution History\",\n            \"family\": \"Family\",\n            // Profile Details\n            \"eligibility_score\": \"Eligibility Score\",\n            \"high_score\": \"High Score\",\n            \"family_size\": \"Family Size\",\n            \"dependents\": \"Dependents\",\n            \"last_distribution\": \"Last Distribution\",\n            \"no_distribution\": \"None\",\n            \"not_distributed_yet\": \"Not distributed yet\",\n            // Contact Information\n            \"contact_info\": \"Contact Information\",\n            \"account_status\": \"Account Status\",\n            \"current_status\": \"Current Status\",\n            \"registration_date\": \"Registration Date\",\n            \"next_review\": \"Next Review\",\n            // Personal Information\n            \"personal_information\": \"Personal Information\",\n            \"basic_information\": \"Basic Information\",\n            \"name_arabic\": \"Name in Arabic\",\n            \"name_english\": \"Name in English\",\n            \"date_of_birth\": \"Date of Birth\",\n            \"gender\": \"Gender\",\n            \"male\": \"Male\",\n            \"female\": \"Female\",\n            // Family Members\n            \"family_members\": \"Family Members\",\n            \"no_family_info\": \"No family information available\",\n            \"dependent\": \"Dependent\",\n            \"special_needs\": \"Special Needs\",\n            \"relationship\": \"Relationship\",\n            \"age\": \"Age\",\n            \"years\": \"years\",\n            \"son\": \"Son\",\n            \"daughter\": \"Daughter\",\n            \"mother\": \"Mother\",\n            \"father\": \"Father\",\n            // Documents\n            \"no_documents\": \"No documents uploaded\",\n            \"verified\": \"Verified\",\n            \"upload_date\": \"Upload Date\",\n            // Coming Soon\n            \"coming_soon\": \"Coming Soon\",\n            \"chart_coming_soon\": \"Chart coming soon\",\n            \"under_development\": \"Under Development\",\n            \"registration_form_coming\": \"Registration Form Under Development\",\n            \"registration_form_desc\": \"New beneficiary registration form will be added soon\",\n            \"will_include_features\": \"The form will include the following features:\",\n            \"multi_step_form\": \"Multi-step form with progress indicator\",\n            \"dual_language_input\": \"Personal data input in Arabic and English\",\n            \"zakat_categories_selection\": \"Selection of eight Zakat categories\",\n            \"document_upload\": \"Required document upload\",\n            \"data_validation\": \"Data validation\",\n            \"duplicate_detection\": \"Automatic duplicate detection\",\n            // System Information\n            \"system_name\": \"Zakat Management System\",\n            \"system_description\": \"Comprehensive system for managing Zakat and assistance requests\",\n            // Dashboard\n            \"role\": \"Role\",\n            \"dashboard_subtitle\": \"Comprehensive system overview\",\n            // Quick Actions Descriptions\n            \"Add a new beneficiary to the system\": \"Add a new beneficiary to the system\",\n            \"Find and manage existing beneficiaries\": \"Find and manage existing beneficiaries\",\n            \"Create distribution and analytics reports\": \"Create distribution and analytics reports\",\n            \"Configure distribution categories and amounts\": \"Configure distribution categories and amounts\",\n            \"Manage system users and permissions\": \"Manage system users and permissions\",\n            \"Configure system preferences\": \"Configure system preferences\",\n            // Recent Activity\n            \"New beneficiary registered\": \"New beneficiary registered\",\n            \"Application approved\": \"Application approved\",\n            \"Zakat distributed\": \"Zakat distributed\",\n            \"Pending review\": \"Pending review\",\n            \"Ahmed Mohammed Al-Rashid has been registered\": \"Ahmed Mohammed Al-Rashid has been registered\",\n            \"Fatima Al-Zahra application approved for Zakat distribution\": \"Fatima Al-Zahra application approved for Zakat distribution\",\n            \"5,000 SAR distributed to 10 beneficiaries\": \"5,000 SAR distributed to 10 beneficiaries\",\n            \"3 applications require case manager review\": \"3 applications require case manager review\",\n            \"Reception Staff\": \"Reception Staff\",\n            \"Case Manager\": \"Case Manager\",\n            \"Finance Manager\": \"Finance Manager\",\n            \"System\": \"System\",\n            // Authentication Messages\n            \"create_new_account\": \"Create new account\",\n            \"sign_in_to_account\": \"Sign in to your account\",\n            \"choose_login_method\": \"Choose the appropriate login method\",\n            \"enter_details_to_create_account\": \"Enter your details to create a new account\",\n            \"confirm_password\": \"Confirm Password\",\n            \"creating_account\": \"Creating account...\",\n            \"already_have_account\": \"Already have an account?\",\n            \"verifying\": \"Verifying...\",\n            \"error\": \"Error\",\n            \"passwords_not_match\": \"Passwords do not match\",\n            \"account_created_success\": \"Account created successfully\",\n            \"wait_admin_approval\": \"Please wait for admin approval of your account\",\n            \"account_creation_error\": \"Error creating account\",\n            \"unexpected_error\": \"An unexpected error occurred\",\n            \"error_during_creation\": \"An error occurred during account creation\",\n            \"invalid_credentials\": \"Invalid credentials entered\",\n            \"welcome_to_system\": \"Welcome to the Zakat Management System\",\n            \"error_during_login\": \"An error occurred during login\",\n            // Account Status\n            \"account_pending_approval\": \"Your account is pending approval\",\n            \"wait_admin_approval_desc\": \"Please wait for admin approval of your account\",\n            // Dashboard\n            \"assigned_tasks\": \"tasks assigned to you\",\n            \"total_requests_desc\": \"Total requests\",\n            \"pending_review_desc\": \"Pending review\",\n            \"approved_today_desc\": \"Approved today\",\n            \"avg_processing_days_desc\": \"Average processing days\",\n            // Reports\n            \"no_reports_access\": \"You do not have permission to access reports\",\n            \"monthly_report\": \"Monthly Report\",\n            \"monthly_stats_desc\": \"Monthly statistics of requests and approvals\",\n            \"requests_label\": \"Requests\",\n            \"approved_label\": \"Approved\",\n            \"rejected_label\": \"Rejected\",\n            // Requests\n            \"back_button\": \"Back\",\n            \"request_details\": \"Request Details\",\n            \"download_decision\": \"Download Decision\",\n            // Gender and Personal Info\n            \"gender_label\": \"Gender\",\n            \"male_label\": \"Male\",\n            \"female_label\": \"Female\",\n            \"marital_status_label\": \"Marital Status\",\n            \"married_label\": \"Married\",\n            \"single_label\": \"Single\",\n            \"divorced_label\": \"Divorced\",\n            \"widowed_label\": \"Widowed\",\n            // Common UI Text\n            \"or\": \"or\",\n            \"no_account\": \"Don't have an account?\",\n            \"demo_accounts\": \"Demo Accounts:\",\n            \"applicant\": \"Applicant:\",\n            \"staff_member\": \"Staff Member:\",\n            // Access Control\n            \"access_denied\": \"Access Denied\",\n            \"no_beneficiary_access\": \"You do not have permission to access beneficiary management\",\n            \"no_registration_access\": \"You do not have permission to register new beneficiaries\",\n            // Multi-Step Form\n            \"step\": \"Step\",\n            \"of\": \"of\",\n            \"complete\": \"Complete\",\n            \"optional\": \"Optional\",\n            \"previous\": \"Previous\",\n            \"next\": \"Next\",\n            \"save_draft\": \"Save Draft\",\n            \"submit\": \"Submit\",\n            // Form Steps\n            \"personal_details_step\": \"Personal Details\",\n            \"contact_information_step\": \"Contact Information\",\n            \"eligibility_criteria_step\": \"Eligibility Criteria\",\n            \"documentation_upload_step\": \"Documentation Upload\",\n            \"review_submit_step\": \"Review & Submit\",\n            // Form Validation\n            \"field_required\": \"This field is required\",\n            \"invalid_email\": \"Invalid email address\",\n            \"invalid_phone\": \"Invalid phone number\",\n            \"invalid_national_id\": \"Invalid national ID\",\n            \"min_length\": \"Must be at least {{min}} characters\",\n            \"max_length\": \"Must not exceed {{max}} characters\",\n            \"invalid_date\": \"Invalid date\",\n            \"future_date_not_allowed\": \"Date cannot be in the future\",\n            \"invalid_arabic_name\": \"Name must contain only Arabic characters\",\n            \"invalid_english_name\": \"Name must contain only English characters\",\n            \"invalid_age\": \"Invalid age\",\n            \"invalid_postal_code\": \"Invalid postal code\",\n            \"select_at_least_one_category\": \"Must select at least one category\",\n            \"min_family_size\": \"Family size must be at least 1\",\n            \"max_family_size\": \"Family size cannot exceed 20\",\n            \"min_dependents\": \"Number of dependents cannot be negative\",\n            \"max_dependents\": \"Number of dependents cannot exceed 19\",\n            \"invalid_income\": \"Invalid monthly income\",\n            \"max_income\": \"Monthly income cannot exceed 50,000 SAR\",\n            \"upload_at_least_one_document\": \"Must upload at least one document\",\n            \"national_id_required\": \"National ID document upload is required\",\n            \"terms_must_be_accepted\": \"Terms and conditions must be accepted\",\n            \"data_accuracy_must_be_confirmed\": \"Data accuracy must be confirmed\",\n            \"privacy_policy_must_be_accepted\": \"Privacy policy must be accepted\",\n            // Personal Details Form\n            \"personal_details_description\": \"Enter the basic personal information for the beneficiary\",\n            \"enter_name_arabic\": \"Enter full name in Arabic\",\n            \"enter_name_english\": \"Enter full name in English\",\n            \"name_arabic_description\": \"Name as written on the national ID\",\n            \"name_english_description\": \"Name in English characters\",\n            \"enter_national_id\": \"Enter national ID number\",\n            \"national_id_description\": \"Saudi national ID number (10 digits)\",\n            \"pick_date\": \"Pick a date\",\n            \"date_of_birth_description\": \"Date of birth as written on the ID\",\n            \"select_gender\": \"Select gender\",\n            \"select_marital_status\": \"Select marital status\",\n            // Contact Information Form\n            \"contact_information_description\": \"Enter contact information and address details\",\n            \"enter_phone_number\": \"Enter phone number\",\n            \"phone_number_description\": \"Mobile phone number (starts with 05 or +966)\",\n            \"enter_email\": \"Enter email address\",\n            \"email_description\": \"Email address for communication (optional)\",\n            \"enter_address\": \"Enter detailed address\",\n            \"address_description\": \"Complete address including district and street\",\n            \"enter_city\": \"Enter city\",\n            \"city_description\": \"City or governorate\",\n            \"select_region\": \"Select region\",\n            \"region_description\": \"Administrative region in Saudi Arabia\",\n            \"enter_postal_code\": \"Enter postal code\",\n            \"postal_code_description\": \"Postal code (5 digits)\",\n            // Eligibility Criteria Form\n            \"islamic_compliance_notice\": \"Islamic Compliance Notice\",\n            \"zakat_categories_description\": \"Beneficiaries are classified according to the eight Zakat categories defined in Islamic law\",\n            \"select_applicable_categories\": \"Select applicable categories for the beneficiary\",\n            \"applicable_categories\": \"Applicable Categories\",\n            \"select_primary_category\": \"Select primary category\",\n            \"primary_category_description\": \"The primary category that the beneficiary belongs to\",\n            \"family_financial_info\": \"Family & Financial Information\",\n            \"family_financial_description\": \"Information about family size and financial situation\",\n            \"family_size_description\": \"Total number of family members\",\n            \"dependents_description\": \"Number of dependent persons\",\n            \"monthly_income_description\": \"Total monthly income in Saudi Riyals\",\n            \"has_special_needs\": \"Has special needs\",\n            \"special_needs_description\": \"Does any family member have special needs?\",\n            \"special_needs_details\": \"Special needs details\",\n            \"describe_special_needs\": \"Describe the special needs in detail\",\n            \"special_needs_details_description\": \"Detailed description of special needs or health conditions\",\n            // Zakat Category Descriptions\n            \"fuqara_description\": \"The Poor - Those who do not have enough to meet their basic needs\",\n            \"masakin_description\": \"The Needy - Those who have less than half of their sufficiency\",\n            \"amilin_description\": \"Zakat Administrators - Those who work in collecting and distributing Zakat\",\n            \"muallafah_description\": \"Those whose hearts are reconciled - Those whose hearts are to be won over to Islam\",\n            \"riqab_description\": \"To free slaves/captives - For freeing slaves and prisoners\",\n            \"gharimin_description\": \"Those in debt - Debtors who cannot pay their debts\",\n            \"fisabilillah_description\": \"In the cause of Allah - For jihad and charitable works\",\n            \"ibnus_sabil_description\": \"The wayfarer/traveler - Travelers stranded away from home\",\n            // Documentation Upload Form\n            \"document_requirements\": \"Document Requirements\",\n            \"document_requirements_description\": \"Please upload the required documents to complete the registration process\",\n            \"required_document_missing\": \"Required Document Missing\",\n            \"national_id_document_required\": \"National ID document upload is required to complete registration\",\n            \"upload_documents\": \"Upload Documents\",\n            \"upload_documents_description\": \"Drag and drop files here or click to browse\",\n            \"drag_drop_files\": \"Drag and drop files here\",\n            \"drop_files_here\": \"Drop files here\",\n            \"or_click_to_browse\": \"Or click to browse\",\n            \"supported_formats\": \"Supported formats\",\n            \"uploaded_documents\": \"Uploaded Documents\",\n            \"uploading\": \"Uploading\",\n            \"required\": \"Required\",\n            \"national_id_document\": \"National ID Document\",\n            \"income_certificate\": \"Income Certificate\",\n            \"family_card\": \"Family Card\",\n            \"medical_report\": \"Medical Report\",\n            \"other_document\": \"Other Document\",\n            \"additional_notes\": \"Additional Notes\",\n            \"additional_notes_description\": \"Any additional information you would like to add\",\n            \"enter_additional_notes\": \"Enter any additional notes\",\n            \"additional_notes_help\": \"Additional information that may help in evaluating the request\",\n            // Review and Submit Form\n            \"review_information\": \"Review Information\",\n            \"review_information_description\": \"Please review all entered information before submitting\",\n            \"not_provided\": \"Not provided\",\n            \"no_documents_uploaded\": \"No documents uploaded\",\n            \"terms_and_conditions\": \"Terms and Conditions\",\n            \"terms_conditions_description\": \"Please read and agree to the terms and conditions\",\n            \"accept_terms_conditions\": \"I accept the terms and conditions\",\n            \"terms_conditions_text\": \"I have read, understood, and agree to the terms and conditions of the Zakat Management System\",\n            \"confirm_data_accuracy\": \"I confirm data accuracy\",\n            \"data_accuracy_text\": \"I confirm that all provided information is accurate and correct\",\n            \"accept_privacy_policy\": \"I accept the privacy policy\",\n            \"privacy_policy_text\": \"I agree to the privacy policy and how personal data is handled\",\n            // Enhanced Validation Messages\n            \"min_value\": \"Value must be at least {{min}}\",\n            \"max_value\": \"Value must not exceed {{max}}\",\n            \"validation_error\": \"Validation error occurred\",\n            \"age_below_18_warning\": \"Age below 18 - may require guardian approval\",\n            \"age_above_100_warning\": \"Age above 100 - please verify the date\",\n            \"dependents_family_size_warning\": \"Number of dependents should be less than family size\",\n            \"high_income_warning\": \"High income - may not be eligible for Zakat\",\n            \"duplicate_beneficiary_found\": \"Similar beneficiary found in the system\",\n            \"duplicate_national_id\": \"National ID already registered\",\n            \"duplicate_phone\": \"Phone number already registered\",\n            \"duplicate_email\": \"Email address already registered\",\n            \"similar_name_found\": \"Similar name found in the system\",\n            // Accessibility Messages\n            \"form_has_error\": \"Form has one error\",\n            \"form_has_errors\": \"Form has {{count}} errors\",\n            \"step_announcement\": \"Step {{current}} of {{total}}: {{title}}\",\n            \"progress_announcement\": \"{{percentage}}% of form completed\",\n            \"required_field\": \"Required field\",\n            \"optional_field\": \"Optional field\",\n            \"error_in_field\": \"Error in field\",\n            \"field_description\": \"Field description\",\n            \"form_navigation\": \"Form navigation\",\n            \"skip_to_content\": \"Skip to content\",\n            \"skip_to_navigation\": \"Skip to navigation\",\n            // Final Implementation Messages\n            \"draft_saved_successfully\": \"Draft saved successfully\",\n            \"error_saving_draft\": \"Error saving draft\",\n            \"duplicate_check_failed\": \"Duplicate check failed\",\n            \"beneficiary_registered_successfully\": \"Beneficiary registered successfully\",\n            \"beneficiary_registration_success_description\": \"The beneficiary has been added to the system and the request will be reviewed\",\n            \"error_submitting_form\": \"Error submitting form\",\n            \"draft_loaded\": \"Saved draft loaded\",\n            \"submitting_registration\": \"Submitting registration\",\n            \"please_wait\": \"Please wait...\",\n            \"beneficiary_registration_description\": \"Register a new beneficiary in the Zakat Management System\",\n            // Assistance Types Management\n            \"assistance_types_management\": \"Assistance Types Management\",\n            \"assistance_types_management_desc\": \"Configure aid types, eligibility criteria, and requirements\",\n            \"add_new_type\": \"Add New Type\",\n            \"create_assistance_type\": \"Create Assistance Type\",\n            \"edit_assistance_type\": \"Edit Assistance Type\",\n            \"assistance_type_details\": \"Assistance Type Details\",\n            \"basic_info_section\": \"Basic Information\",\n            \"configure_basic_details\": \"Configure the basic details of the assistance type\",\n            \"assistance_name_arabic\": \"Name (Arabic)\",\n            \"assistance_name_english\": \"Name (English)\",\n            \"description_arabic\": \"Description (Arabic)\",\n            \"description_english\": \"Description (English)\",\n            \"maximum_amount\": \"Maximum Amount\",\n            \"maximum_amount_sar\": \"Maximum Amount (SAR)\",\n            \"category\": \"Category\",\n            \"active_status\": \"Active Status\",\n            \"enable_assistance_type\": \"Enable this assistance type for applications\",\n            \"required_documents\": \"Required Documents\",\n            \"configure_required_documents\": \"Configure documents required for this assistance type\",\n            \"add_document\": \"Add Document\",\n            \"no_documents_configured\": \"No documents configured yet\",\n            \"click_add_document\": 'Click \"Add Document\" to get started',\n            \"document_name_arabic\": \"Name (Arabic)\",\n            \"document_name_english\": \"Name (English)\",\n            \"is_required\": \"Required\",\n            \"max_size_kb\": \"Max Size (KB)\",\n            \"accepted_formats\": \"Accepted Formats\",\n            \"total_types\": \"Total Types\",\n            \"active_types\": \"Active Types\",\n            \"inactive_types\": \"Inactive Types\",\n            \"max_amount\": \"Max Amount\",\n            \"showing_types\": \"Showing {{count}} of {{total}} assistance types\",\n            \"no_assistance_types\": \"No assistance types found\",\n            \"search_assistance_types\": \"Search assistance types...\",\n            \"view_details\": \"View Details\",\n            \"activate\": \"Activate\",\n            \"deactivate\": \"Deactivate\",\n            \"assistance_type_not_found\": \"Assistance Type Not Found\",\n            \"assistance_type_not_found_desc\": \"The requested assistance type could not be found\",\n            \"back_to_assistance_types\": \"Back to Assistance Types\",\n            \"create_new_assistance_type\": \"Create New Assistance Type\",\n            \"configure_assistance_type\": \"Configure a new assistance type with eligibility criteria and requirements\",\n            \"back_to_details\": \"Back to Details\",\n            \"modify_assistance_type\": \"Modify the configuration of this assistance type\",\n            \"update_assistance_type\": \"Update Assistance Type\",\n            \"assistance_type_created\": \"Assistance type created successfully\",\n            \"assistance_type_updated\": \"Assistance type updated successfully\",\n            \"failed_create_assistance_type\": \"Failed to create assistance type\",\n            \"failed_update_assistance_type\": \"Failed to update assistance type\",\n            \"cancel\": \"Cancel\",\n            \"create\": \"Create\",\n            \"update\": \"Update\",\n            \"saving\": \"Saving...\",\n            \"eligibility_criteria\": \"Eligibility Criteria\",\n            \"usage_statistics\": \"Usage Statistics\",\n            \"total_applications\": \"Total Applications\",\n            \"approved_requests\": \"Approved\",\n            \"rejected_requests\": \"Rejected\",\n            // Common Admin Terms\n            \"manage\": \"Manage\",\n            \"configure\": \"Configure\",\n            \"actions\": \"Actions\",\n            \"details\": \"Details\",\n            \"admin_overview\": \"Overview\",\n            \"admin_statistics\": \"Statistics\",\n            \"all_operational\": \"All operational\",\n            \"good\": \"Good\",\n            \"all_systems_operational\": \"All systems operational\",\n            \"recently_updated\": \"Recently updated\",\n            \"from_last_month\": \"From last month\",\n            \"documents_count\": \"{{count}} documents\",\n            \"uses\": \"uses\",\n            \"formats\": \"Formats\",\n            \"size\": \"Size\",\n            \"mb\": \"MB\",\n            \"status_active\": \"Active\",\n            \"status_inactive\": \"Inactive\",\n            \"configure_requirements\": \"Configure Requirements\",\n            \"advanced_filters\": \"Advanced Filters\",\n            \"most_used\": \"Most Used\",\n            \"total_usage\": \"Total Usage\",\n            // User Management\n            \"manage_user_accounts_roles_permissions\": \"Manage user accounts, roles, and permissions\",\n            \"add_new_user\": \"Add New User\",\n            \"search_users_placeholder\": \"Search users by name, email, or role...\",\n            \"active_users\": \"Active Users\",\n            \"pending_approval\": \"Pending Approval\",\n            \"admin_users\": \"Admin Users\",\n            \"system_users\": \"System Users\",\n            \"showing_users\": \"Showing {{count}} of {{total}} users\",\n            \"user\": \"User\",\n            \"last_login\": \"Last Login\",\n            \"created\": \"Created\",\n            \"no_users_found\": \"No users found\",\n            \"never\": \"Never\",\n            \"edit_user\": \"Edit User\",\n            \"reset_password\": \"Reset Password\",\n            \"change_role\": \"Change Role\",\n            \"suspend_user\": \"Suspend User\",\n            \"activate_user\": \"Activate User\",\n            // Status values\n            \"active\": \"Active\",\n            // Workflow Management\n            \"configure_approval_workflows_business_rules\": \"Configure approval workflows and business rules for request processing\",\n            \"create_workflow\": \"Create Workflow\",\n            \"active_workflows\": \"Active Workflows\",\n            \"avg_steps\": \"Avg Steps\",\n            \"active_roles\": \"Active Roles\",\n            \"workflows\": \"Workflows\",\n            \"role_configuration\": \"Role Configuration\",\n            \"business_rules\": \"Business Rules\",\n            \"testing_simulation\": \"Testing & Simulation\",\n            \"financial_support_workflow\": \"Financial Support Workflow\",\n            \"standard_workflow_financial_assistance\": \"Standard workflow for financial assistance requests\",\n            \"medical_assistance_workflow\": \"Medical Assistance Workflow\",\n            \"specialized_workflow_medical_aid\": \"Specialized workflow for medical aid requests\",\n            \"emergency_relief_workflow\": \"Emergency Relief Workflow\",\n            \"fast_track_workflow_emergency\": \"Fast-track workflow for emergency situations\",\n            \"steps\": \"steps\",\n            \"requests_processed\": \"requests processed\",\n            \"test\": \"Test\",\n            // Document Configuration\n            \"manage_document_types_requirements\": \"Manage document types and requirements for assistance applications\",\n            \"add_document_type\": \"Add Document Type\",\n            \"search_document_types_placeholder\": \"Search document types...\",\n            \"document_types\": \"Document Types\",\n            \"requirements_mapping\": \"Requirements Mapping\",\n            \"validation_rules\": \"Validation Rules\",\n            \"showing_document_types\": \"Showing {{count}} of {{total}} document types\",\n            \"max_size\": \"Max Size\",\n            \"usage_count\": \"Usage Count\",\n            \"no_document_types_found\": \"No document types found\",\n            \"document_requirements_mapping\": \"Document Requirements Mapping\",\n            \"configure_document_requirements_per_assistance_type\": \"Configure which documents are required for each assistance type\",\n            \"requirements_mapping_interface\": \"Requirements mapping interface\",\n            \"configure_document_requirements_per_assistance_type_desc\": \"Configure document requirements per assistance type\",\n            \"validation_rules_title\": \"Validation Rules\",\n            \"configure_file_format_size_validation\": \"Configure file format and size validation rules\",\n            \"validation_rules_interface\": \"Validation rules interface\",\n            \"configure_file_validation_parameters\": \"Configure file validation parameters\",\n            // System Settings\n            \"configure_general_system_parameters\": \"Configure general system parameters and operational settings\",\n            \"save_changes\": \"Save Changes\",\n            \"general\": \"General\",\n            \"calculation\": \"Calculation\",\n            \"notifications\": \"Notifications\",\n            \"limits\": \"Limits\",\n            \"backup\": \"Backup\",\n            \"organization_information\": \"Organization Information\",\n            \"basic_organization_contact_info\": \"Basic organization details and contact information\",\n            \"organization_name_english\": \"Organization Name (English)\",\n            \"organization_name_arabic\": \"Organization Name (Arabic)\",\n            \"contact_email\": \"Contact Email\",\n            \"contact_phone\": \"Contact Phone\",\n            \"address\": \"Address\",\n            \"zakat_calculation_parameters\": \"Zakat Calculation Parameters\",\n            \"configure_nisab_calculation_methods\": \"Configure Nisab values and calculation methods\",\n            \"nisab_gold_grams\": \"Nisab Gold (grams)\",\n            \"nisab_silver_grams\": \"Nisab Silver (grams)\",\n            \"zakat_rate_percentage\": \"Zakat Rate (%)\",\n            \"use_hijri_calendar\": \"Use Hijri Calendar\",\n            \"use_islamic_calendar_zakat_calculations\": \"Use Islamic calendar for Zakat calculations\",\n            \"notification_preferences\": \"Notification Preferences\",\n            \"configure_system_notifications_alerts\": \"Configure system notifications and alerts\",\n            \"email_notifications\": \"Email Notifications\",\n            \"send_notifications_via_email\": \"Send notifications via email\",\n            \"sms_notifications\": \"SMS Notifications\",\n            \"send_notifications_via_sms\": \"Send notifications via SMS\",\n            \"system_alerts\": \"System Alerts\",\n            \"show_in_app_system_alerts\": \"Show in-app system alerts\",\n            \"system_limits_thresholds\": \"System Limits and Thresholds\",\n            \"configure_operational_limits_security\": \"Configure operational limits and security parameters\",\n            \"max_request_amount_sar\": \"Max Request Amount (SAR)\",\n            \"session_timeout_minutes\": \"Session Timeout (minutes)\",\n            \"max_file_size_mb\": \"Max File Size (MB)\",\n            \"backup_maintenance\": \"Backup and Maintenance\",\n            \"configure_backup_data_retention\": \"Configure backup settings and data retention policies\",\n            \"automatic_backup\": \"Automatic Backup\",\n            \"enable_automatic_system_backups\": \"Enable automatic system backups\",\n            \"backup_frequency\": \"Backup Frequency\",\n            \"hourly\": \"Hourly\",\n            \"daily\": \"Daily\",\n            \"weekly\": \"Weekly\",\n            \"monthly\": \"Monthly\",\n            \"retention_period_days\": \"Retention Period (days)\",\n            \"success\": \"Success\",\n            \"system_settings_saved_successfully\": \"System settings saved successfully\",\n            \"failed_to_save_settings\": \"Failed to save settings\",\n            // Audit Trail\n            \"view_system_activity_logs_compliance\": \"View system activity logs and audit trails for compliance monitoring\",\n            \"export_logs\": \"Export Logs\",\n            \"search_audit_logs_placeholder\": \"Search audit logs by user, action, or entity...\",\n            \"total_events\": \"Total Events\",\n            \"security_events\": \"Security Events\",\n            \"warnings\": \"Warnings\",\n            \"todays_events\": \"Today's Events\",\n            \"system_activity_logs\": \"System Activity Logs\",\n            \"showing_audit_events\": \"Showing {{count}} of {{total}} audit events\",\n            \"timestamp\": \"Timestamp\",\n            \"action\": \"Action\",\n            \"entity\": \"Entity\",\n            \"severity\": \"Severity\",\n            \"ip_address\": \"IP Address\",\n            \"no_audit_logs_found\": \"No audit logs found\",\n            \"common_audit_operations_reports\": \"Common audit trail operations and reports\",\n            \"security_report\": \"Security Report\",\n            \"generate_security_events_report\": \"Generate security events report\",\n            \"user_activity_report\": \"User Activity Report\",\n            \"export_user_activity_summary\": \"Export user activity summary\",\n            \"compliance_report\": \"Compliance Report\",\n            \"generate_compliance_audit_report\": \"Generate compliance audit report\"\n        }\n    }\n};\n// Initialize i18n immediately with resources\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    fallbackLng: \"ar\",\n    lng: \"ar\",\n    debug: \"development\" === \"development\",\n    detection: {\n        order: [\n            \"localStorage\",\n            \"navigator\",\n            \"htmlTag\"\n        ],\n        caches: [\n            \"localStorage\"\n        ]\n    },\n    interpolation: {\n        escapeValue: false\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProvider: () => (/* binding */ I18nProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ I18nProvider auto */ \n\n\nfunction I18nProvider({ children }) {\n    // Always render the I18nextProvider to avoid hydration mismatch\n    // The i18n instance is already initialized with resources in lib/i18n.ts\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_i18next__WEBPACK_IMPORTED_MODULE_1__.I18nextProvider, {\n        i18n: _lib_i18n__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\i18n-provider.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvaTE4bi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBSStDO0FBQ2xCO0FBTXRCLFNBQVNFLGFBQWEsRUFBRUMsUUFBUSxFQUFxQjtJQUMxRCxnRUFBZ0U7SUFDaEUseUVBQXlFO0lBQ3pFLHFCQUNFLDhEQUFDSCwwREFBZUE7UUFBQ0MsTUFBTUEsaURBQUlBO2tCQUN4QkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL2kxOG4tcHJvdmlkZXIudHN4PzVkYjIiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBJMThuZXh0UHJvdmlkZXIgfSBmcm9tICdyZWFjdC1pMThuZXh0J1xuaW1wb3J0IGkxOG4gZnJvbSAnQC9saWIvaTE4bidcblxuaW50ZXJmYWNlIEkxOG5Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gSTE4blByb3ZpZGVyKHsgY2hpbGRyZW4gfTogSTE4blByb3ZpZGVyUHJvcHMpIHtcbiAgLy8gQWx3YXlzIHJlbmRlciB0aGUgSTE4bmV4dFByb3ZpZGVyIHRvIGF2b2lkIGh5ZHJhdGlvbiBtaXNtYXRjaFxuICAvLyBUaGUgaTE4biBpbnN0YW5jZSBpcyBhbHJlYWR5IGluaXRpYWxpemVkIHdpdGggcmVzb3VyY2VzIGluIGxpYi9pMThuLnRzXG4gIHJldHVybiAoXG4gICAgPEkxOG5leHRQcm92aWRlciBpMThuPXtpMThufT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0kxOG5leHRQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkkxOG5leHRQcm92aWRlciIsImkxOG4iLCJJMThuUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/i18n-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SessionProvider auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\session-provider.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvc2Vzc2lvbi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRzRFO0FBT3JFLFNBQVNBLGdCQUFnQixFQUFFRSxRQUFRLEVBQXdCO0lBQ2hFLHFCQUFPLDhEQUFDRCw0REFBdUJBO2tCQUFFQzs7Ozs7O0FBQ25DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL3Nlc3Npb24tcHJvdmlkZXIudHN4PzIzNWEiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBTZXNzaW9uUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/session-provider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"515874a36e9a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9hcHAvZ2xvYmFscy5jc3M/YjcwMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjUxNTg3NGEzNmU5YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers_session_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/session-provider */ \"(rsc)/./providers/session-provider.tsx\");\n/* harmony import */ var _providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/i18n-provider */ \"(rsc)/./providers/i18n-provider.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"نظام إدارة الزكاة - Zakat Management System\",\n    description: \"نظام شامل لإدارة طلبات الزكاة والمساعدات\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} font-sans antialiased`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_session_provider__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__.I18nProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\nfunction HomePage() {\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect)(\"/auth/login\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7QUFDMEM7QUFFM0IsU0FBU0M7SUFDdEJELHlEQUFRQSxDQUFDO0FBQ1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmltcG9ydCB7IHJlZGlyZWN0IH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lUGFnZSgpIHtcbiAgcmVkaXJlY3QoJy9hdXRoL2xvZ2luJylcbn1cbiJdLCJuYW1lcyI6WyJyZWRpcmVjdCIsIkhvbWVQYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   I18nProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\i18n-provider.tsx#I18nProvider`);


/***/ }),

/***/ "(rsc)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SessionProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\session-provider.tsx#SessionProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/i18next","vendor-chunks/react-i18next","vendor-chunks/i18next-browser-languagedetector","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/html-parse-stringify","vendor-chunks/void-elements"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();