[{"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\assistance-types\\new\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\assistance-types\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\assistance-types\\[id]\\edit\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\assistance-types\\[id]\\page.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\audit\\page.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\distribution\\page.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\documents\\page.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\page.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\settings\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\users\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\workflow\\page.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\api\\auth\\[...nextauth]\\route.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\auth\\login\\page.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\auth\\register\\page.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\beneficiaries\\new\\page.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\beneficiaries\\page.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\beneficiaries\\[id]\\page.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\dashboard\\page.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\layout.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\page.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\profile\\page.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\reports\\page.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\requests\\new\\page.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\requests\\page.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\requests\\[id]\\page.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\signup\\route.ts": "26", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\tasks\\page.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\test\\route.ts": "28", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\test-form\\page.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\admin\\admin-breadcrumb.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\admin\\admin-layout.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\admin\\admin-route-guard.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\admin\\admin-sidebar.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\admin\\assistance-type-form.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\enhanced-stats.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\quick-actions.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\recent-activity.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\recent-requests.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\stats-card.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\summary-widget.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\welcome-banner.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\debug\\i18n-test.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\multi-step-form.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\steps\\contact-information-step.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\steps\\documentation-upload-step.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\steps\\eligibility-criteria-step.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\steps\\personal-details-step.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\steps\\review-submit-step.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\layout\\dashboard-layout.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\layout\\navbar.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\layout\\sidebar.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\layout\\user-menu.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\theme-provider.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\accordion.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\alert-dialog.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\alert.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\aspect-ratio.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\avatar.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\badge.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\breadcrumb.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\button.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\calendar.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\card.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\carousel.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\checkbox.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\collapsible.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\command.tsx": "67", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\context-menu.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\date-range-picker.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\dialog.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\drawer.tsx": "71", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\dropdown-menu.tsx": "72", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\form.tsx": "73", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\hover-card.tsx": "74", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\input-otp.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\input.tsx": "76", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\islamic-pattern.tsx": "77", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\label.tsx": "78", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\language-switcher.tsx": "79", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\loading-spinner.tsx": "80", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\menubar.tsx": "81", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\navigation-menu.tsx": "82", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\pagination.tsx": "83", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\popover.tsx": "84", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\progress.tsx": "85", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\radio-group.tsx": "86", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\resizable.tsx": "87", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\role-badge.tsx": "88", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\scroll-area.tsx": "89", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\select.tsx": "90", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\separator.tsx": "91", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\sheet.tsx": "92", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\skeleton.tsx": "93", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\slider.tsx": "94", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\sonner.tsx": "95", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\status-badge.tsx": "96", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\switch.tsx": "97", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\table.tsx": "98", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\tabs.tsx": "99", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\task-card.tsx": "100", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\textarea.tsx": "101", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\theme-toggle.tsx": "102", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\toast.tsx": "103", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\toaster.tsx": "104", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\toggle-group.tsx": "105", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\toggle.tsx": "106", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\tooltip.tsx": "107", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\use-toast.ts": "108", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\i18n.ts": "109", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\mock-data.ts": "110", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\types.ts": "111", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\utils\\accessibility.ts": "112", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\utils\\form-validation.ts": "113", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\utils.ts": "114", "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\validation\\beneficiary-registration.ts": "115"}, {"size": 2260, "mtime": 1755442708294, "results": "116", "hashOfConfig": "117"}, {"size": 10626, "mtime": 1755447268338, "results": "118", "hashOfConfig": "117"}, {"size": 3035, "mtime": 1755442773257, "results": "119", "hashOfConfig": "117"}, {"size": 11996, "mtime": 1755442756942, "results": "120", "hashOfConfig": "117"}, {"size": 11496, "mtime": 1755447660585, "results": "121", "hashOfConfig": "117"}, {"size": 9853, "mtime": 1755442896686, "results": "122", "hashOfConfig": "117"}, {"size": 14261, "mtime": 1755447470280, "results": "123", "hashOfConfig": "117"}, {"size": 6275, "mtime": 1755443998116, "results": "124", "hashOfConfig": "117"}, {"size": 15826, "mtime": 1755447561925, "results": "125", "hashOfConfig": "117"}, {"size": 10198, "mtime": 1755447803841, "results": "126", "hashOfConfig": "117"}, {"size": 8649, "mtime": 1755447350976, "results": "127", "hashOfConfig": "117"}, {"size": 2093, "mtime": 1755422398340, "results": "128", "hashOfConfig": "117"}, {"size": 7454, "mtime": 1755433279172, "results": "129", "hashOfConfig": "117"}, {"size": 8611, "mtime": 1755445879837, "results": "130", "hashOfConfig": "117"}, {"size": 9021, "mtime": 1755441177014, "results": "131", "hashOfConfig": "117"}, {"size": 14320, "mtime": 1755431547302, "results": "132", "hashOfConfig": "117"}, {"size": 17998, "mtime": 1755433143174, "results": "133", "hashOfConfig": "117"}, {"size": 4893, "mtime": 1755445943641, "results": "134", "hashOfConfig": "117"}, {"size": 1172, "mtime": 1755436527123, "results": "135", "hashOfConfig": "117"}, {"size": 110, "mtime": 1755422398240, "results": "136", "hashOfConfig": "117"}, {"size": 14578, "mtime": 1755422398368, "results": "137", "hashOfConfig": "117"}, {"size": 13898, "mtime": 1755433046462, "results": "138", "hashOfConfig": "117"}, {"size": 14703, "mtime": 1755422398294, "results": "139", "hashOfConfig": "117"}, {"size": 9284, "mtime": 1755438122201, "results": "140", "hashOfConfig": "117"}, {"size": 16947, "mtime": 1755433082114, "results": "141", "hashOfConfig": "117"}, {"size": 1953, "mtime": 1755422398348, "results": "142", "hashOfConfig": "117"}, {"size": 12921, "mtime": 1755422398358, "results": "143", "hashOfConfig": "117"}, {"size": 148, "mtime": 1755422398265, "results": "144", "hashOfConfig": "117"}, {"size": 6587, "mtime": 1755440540114, "results": "145", "hashOfConfig": "117"}, {"size": 2496, "mtime": 1755442423781, "results": "146", "hashOfConfig": "117"}, {"size": 1464, "mtime": 1755442597282, "results": "147", "hashOfConfig": "117"}, {"size": 2404, "mtime": 1755442449924, "results": "148", "hashOfConfig": "117"}, {"size": 4203, "mtime": 1755443953330, "results": "149", "hashOfConfig": "117"}, {"size": 13041, "mtime": 1755443598675, "results": "150", "hashOfConfig": "117"}, {"size": 4695, "mtime": 1755436917411, "results": "151", "hashOfConfig": "117"}, {"size": 3927, "mtime": 1755437337192, "results": "152", "hashOfConfig": "117"}, {"size": 5535, "mtime": 1755437364620, "results": "153", "hashOfConfig": "117"}, {"size": 3189, "mtime": 1755422397894, "results": "154", "hashOfConfig": "117"}, {"size": 2130, "mtime": 1755422397888, "results": "155", "hashOfConfig": "117"}, {"size": 2291, "mtime": 1755436560432, "results": "156", "hashOfConfig": "117"}, {"size": 3830, "mtime": 1755436866364, "results": "157", "hashOfConfig": "117"}, {"size": 1513, "mtime": 1755448043702, "results": "158", "hashOfConfig": "117"}, {"size": 9301, "mtime": 1755438668626, "results": "159", "hashOfConfig": "117"}, {"size": 11125, "mtime": 1755441209126, "results": "160", "hashOfConfig": "117"}, {"size": 13396, "mtime": 1755441247250, "results": "161", "hashOfConfig": "117"}, {"size": 15012, "mtime": 1755441233653, "results": "162", "hashOfConfig": "117"}, {"size": 11158, "mtime": 1755441197739, "results": "163", "hashOfConfig": "117"}, {"size": 14646, "mtime": 1755441259329, "results": "164", "hashOfConfig": "117"}, {"size": 1808, "mtime": 1755436539489, "results": "165", "hashOfConfig": "117"}, {"size": 4082, "mtime": 1755436587560, "results": "166", "hashOfConfig": "117"}, {"size": 3144, "mtime": 1755436603374, "results": "167", "hashOfConfig": "117"}, {"size": 2353, "mtime": 1755443561804, "results": "168", "hashOfConfig": "117"}, {"size": 327, "mtime": 1755422397879, "results": "169", "hashOfConfig": "117"}, {"size": 2004, "mtime": 1755422398196, "results": "170", "hashOfConfig": "117"}, {"size": 4459, "mtime": 1755422397998, "results": "171", "hashOfConfig": "117"}, {"size": 1595, "mtime": 1755422398074, "results": "172", "hashOfConfig": "117"}, {"size": 158, "mtime": 1755422398056, "results": "173", "hashOfConfig": "117"}, {"size": 1430, "mtime": 1755422397964, "results": "174", "hashOfConfig": "117"}, {"size": 1226, "mtime": 1755435510135, "results": "175", "hashOfConfig": "117"}, {"size": 2724, "mtime": 1755422397952, "results": "176", "hashOfConfig": "117"}, {"size": 2176, "mtime": 1755436747397, "results": "177", "hashOfConfig": "117"}, {"size": 2633, "mtime": 1755422398160, "results": "178", "hashOfConfig": "117"}, {"size": 1943, "mtime": 1755436799983, "results": "179", "hashOfConfig": "117"}, {"size": 6296, "mtime": 1755422398108, "results": "180", "hashOfConfig": "117"}, {"size": 1078, "mtime": 1755422397947, "results": "181", "hashOfConfig": "117"}, {"size": 335, "mtime": 1755422398127, "results": "182", "hashOfConfig": "117"}, {"size": 4920, "mtime": 1755422397976, "results": "183", "hashOfConfig": "117"}, {"size": 7294, "mtime": 1755422397957, "results": "184", "hashOfConfig": "117"}, {"size": 1791, "mtime": 1755422397913, "results": "185", "hashOfConfig": "117"}, {"size": 3871, "mtime": 1755422398086, "results": "186", "hashOfConfig": "117"}, {"size": 3043, "mtime": 1755422397982, "results": "187", "hashOfConfig": "117"}, {"size": 7343, "mtime": 1755422397970, "results": "188", "hashOfConfig": "117"}, {"size": 4150, "mtime": 1755422398043, "results": "189", "hashOfConfig": "117"}, {"size": 1207, "mtime": 1755422397941, "results": "190", "hashOfConfig": "117"}, {"size": 2185, "mtime": 1755422398080, "results": "191", "hashOfConfig": "117"}, {"size": 823, "mtime": 1755422398101, "results": "192", "hashOfConfig": "117"}, {"size": 2000, "mtime": 1755436767217, "results": "193", "hashOfConfig": "117"}, {"size": 723, "mtime": 1755422398092, "results": "194", "hashOfConfig": "117"}, {"size": 1388, "mtime": 1755422398144, "results": "195", "hashOfConfig": "117"}, {"size": 2849, "mtime": 1755436822095, "results": "196", "hashOfConfig": "117"}, {"size": 8025, "mtime": 1755422398151, "results": "197", "hashOfConfig": "117"}, {"size": 5067, "mtime": 1755422398186, "results": "198", "hashOfConfig": "117"}, {"size": 2772, "mtime": 1755422398139, "results": "199", "hashOfConfig": "117"}, {"size": 1253, "mtime": 1755422397936, "results": "200", "hashOfConfig": "117"}, {"size": 798, "mtime": 1755422398010, "results": "201", "hashOfConfig": "117"}, {"size": 1493, "mtime": 1755422398019, "results": "202", "hashOfConfig": "117"}, {"size": 1732, "mtime": 1755422398033, "results": "203", "hashOfConfig": "117"}, {"size": 628, "mtime": 1755422398181, "results": "204", "hashOfConfig": "117"}, {"size": 1665, "mtime": 1755422398005, "results": "205", "hashOfConfig": "117"}, {"size": 5652, "mtime": 1755422398096, "results": "206", "hashOfConfig": "117"}, {"size": 777, "mtime": 1755422398029, "results": "207", "hashOfConfig": "117"}, {"size": 4305, "mtime": 1755422398023, "results": "208", "hashOfConfig": "117"}, {"size": 264, "mtime": 1755422397987, "results": "209", "hashOfConfig": "117"}, {"size": 1098, "mtime": 1755422397905, "results": "210", "hashOfConfig": "117"}, {"size": 902, "mtime": 1755422397992, "results": "211", "hashOfConfig": "117"}, {"size": 1481, "mtime": 1755422398038, "results": "212", "hashOfConfig": "117"}, {"size": 1160, "mtime": 1755422398191, "results": "213", "hashOfConfig": "117"}, {"size": 2784, "mtime": 1755422398066, "results": "214", "hashOfConfig": "117"}, {"size": 1909, "mtime": 1755422398122, "results": "215", "hashOfConfig": "117"}, {"size": 1997, "mtime": 1755422398135, "results": "216", "hashOfConfig": "117"}, {"size": 778, "mtime": 1755422398115, "results": "217", "hashOfConfig": "117"}, {"size": 1233, "mtime": 1755443548125, "results": "218", "hashOfConfig": "117"}, {"size": 5026, "mtime": 1755436842809, "results": "219", "hashOfConfig": "117"}, {"size": 787, "mtime": 1755422398175, "results": "220", "hashOfConfig": "117"}, {"size": 1767, "mtime": 1755422398051, "results": "221", "hashOfConfig": "117"}, {"size": 1458, "mtime": 1755422398165, "results": "222", "hashOfConfig": "117"}, {"size": 1169, "mtime": 1755422397924, "results": "223", "hashOfConfig": "117"}, {"size": 4005, "mtime": 1755422398014, "results": "224", "hashOfConfig": "117"}, {"size": 83959, "mtime": 1755447792246, "results": "225", "hashOfConfig": "117"}, {"size": 28565, "mtime": 1755443172585, "results": "226", "hashOfConfig": "117"}, {"size": 7818, "mtime": 1755430945561, "results": "227", "hashOfConfig": "117"}, {"size": 10252, "mtime": 1755439680178, "results": "228", "hashOfConfig": "117"}, {"size": 9686, "mtime": 1755439941178, "results": "229", "hashOfConfig": "117"}, {"size": 498, "mtime": 1755422398397, "results": "230", "hashOfConfig": "117"}, {"size": 7219, "mtime": 1755440007805, "results": "231", "hashOfConfig": "117"}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "11792r", {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\assistance-types\\new\\page.tsx", ["577", "578", "579"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\assistance-types\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\assistance-types\\[id]\\edit\\page.tsx", ["580", "581"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\assistance-types\\[id]\\page.tsx", ["582", "583"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\audit\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\distribution\\page.tsx", ["584", "585"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\documents\\page.tsx", ["586"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\settings\\page.tsx", ["587", "588", "589"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\users\\page.tsx", ["590"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\workflow\\page.tsx", ["591"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\api\\auth\\[...nextauth]\\route.ts", ["592", "593", "594"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\auth\\login\\page.tsx", ["595", "596"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\auth\\register\\page.tsx", ["597", "598"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\beneficiaries\\new\\page.tsx", ["599", "600", "601", "602", "603", "604", "605"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\beneficiaries\\page.tsx", ["606", "607", "608", "609", "610", "611", "612", "613", "614"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\beneficiaries\\[id]\\page.tsx", ["615", "616", "617"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\profile\\page.tsx", ["618", "619"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\reports\\page.tsx", ["620", "621", "622"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\requests\\new\\page.tsx", ["623"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\requests\\page.tsx", ["624", "625"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\requests\\[id]\\page.tsx", ["626", "627"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\signup\\route.ts", ["628"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\tasks\\page.tsx", ["629", "630", "631", "632", "633", "634", "635"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\test\\route.ts", ["636"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\test-form\\page.tsx", ["637", "638"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\admin\\admin-breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\admin\\admin-layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\admin\\admin-route-guard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\admin\\admin-sidebar.tsx", ["639", "640"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\admin\\assistance-type-form.tsx", ["641", "642", "643", "644"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\enhanced-stats.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\quick-actions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\recent-activity.tsx", ["645"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\recent-requests.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\stats-card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\summary-widget.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\dashboard\\welcome-banner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\debug\\i18n-test.tsx", ["646", "647", "648", "649", "650", "651"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\multi-step-form.tsx", ["652", "653", "654", "655", "656", "657", "658", "659", "660", "661"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\steps\\contact-information-step.tsx", ["662", "663", "664"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\steps\\documentation-upload-step.tsx", ["665", "666", "667"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\steps\\eligibility-criteria-step.tsx", ["668", "669", "670", "671"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\steps\\personal-details-step.tsx", ["672", "673", "674"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\forms\\steps\\review-submit-step.tsx", ["675", "676", "677", "678", "679", "680", "681", "682"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\layout\\dashboard-layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\layout\\navbar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\layout\\sidebar.tsx", ["683", "684", "685", "686", "687"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\layout\\user-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\aspect-ratio.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\calendar.tsx", ["688", "689"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\carousel.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\collapsible.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\command.tsx", ["690"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\context-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\date-range-picker.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\drawer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\hover-card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\input-otp.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\input.tsx", ["691"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\islamic-pattern.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\language-switcher.tsx", ["692"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\loading-spinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\menubar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\pagination.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\resizable.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\role-badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\status-badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\task-card.tsx", ["693"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\textarea.tsx", ["694"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\toaster.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\toggle-group.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\toggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\ui\\use-toast.ts", ["695"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\i18n.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\mock-data.ts", ["696", "697", "698", "699", "700"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\types.ts", ["701"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\utils\\accessibility.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\utils\\form-validation.ts", ["702", "703", "704", "705", "706", "707", "708", "709", "710"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\lib\\validation\\beneficiary-registration.ts", ["711", "712", "713", "714", "715", "716", "717"], [], {"ruleId": "718", "severity": 2, "message": "719", "line": 10, "column": 15, "nodeType": null, "messageId": "720", "endLine": 10, "endColumn": 29}, {"ruleId": "718", "severity": 2, "message": "721", "line": 14, "column": 11, "nodeType": null, "messageId": "720", "endLine": 14, "endColumn": 12}, {"ruleId": "718", "severity": 2, "message": "722", "line": 40, "column": 14, "nodeType": null, "messageId": "720", "endLine": 40, "endColumn": 19}, {"ruleId": "718", "severity": 2, "message": "721", "line": 16, "column": 11, "nodeType": null, "messageId": "720", "endLine": 16, "endColumn": 12}, {"ruleId": "718", "severity": 2, "message": "722", "line": 61, "column": 14, "nodeType": null, "messageId": "720", "endLine": 61, "endColumn": 19}, {"ruleId": "718", "severity": 2, "message": "721", "line": 24, "column": 11, "nodeType": null, "messageId": "720", "endLine": 24, "endColumn": 12}, {"ruleId": "718", "severity": 2, "message": "723", "line": 198, "column": 65, "nodeType": null, "messageId": "720", "endLine": 198, "endColumn": 70}, {"ruleId": "718", "severity": 2, "message": "724", "line": 3, "column": 10, "nodeType": null, "messageId": "720", "endLine": 3, "endColumn": 18}, {"ruleId": "718", "severity": 2, "message": "721", "line": 22, "column": 11, "nodeType": null, "messageId": "720", "endLine": 22, "endColumn": 12}, {"ruleId": "718", "severity": 2, "message": "725", "line": 83, "column": 25, "nodeType": null, "messageId": "720", "endLine": 83, "endColumn": 41}, {"ruleId": "718", "severity": 2, "message": "726", "line": 14, "column": 3, "nodeType": null, "messageId": "720", "endLine": 14, "endColumn": 11}, {"ruleId": "718", "severity": 2, "message": "722", "line": 69, "column": 14, "nodeType": null, "messageId": "720", "endLine": 69, "endColumn": 19}, {"ruleId": "727", "severity": 2, "message": "728", "line": 78, "column": 46, "nodeType": "729", "messageId": "730", "endLine": 78, "endColumn": 49, "suggestions": "731"}, {"ruleId": "718", "severity": 2, "message": "732", "line": 35, "column": 17, "nodeType": null, "messageId": "720", "endLine": 35, "endColumn": 25}, {"ruleId": "718", "severity": 2, "message": "724", "line": 3, "column": 10, "nodeType": null, "messageId": "720", "endLine": 3, "endColumn": 18}, {"ruleId": "718", "severity": 2, "message": "733", "line": 4, "column": 8, "nodeType": null, "messageId": "720", "endLine": 4, "endColumn": 14}, {"ruleId": "727", "severity": 2, "message": "728", "line": 54, "column": 32, "nodeType": "729", "messageId": "730", "endLine": 54, "endColumn": 35, "suggestions": "734"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 62, "column": 39, "nodeType": "729", "messageId": "730", "endLine": 62, "endColumn": 42, "suggestions": "735"}, {"ruleId": "718", "severity": 2, "message": "736", "line": 22, "column": 14, "nodeType": null, "messageId": "720", "endLine": 22, "endColumn": 18}, {"ruleId": "718", "severity": 2, "message": "722", "line": 49, "column": 14, "nodeType": null, "messageId": "720", "endLine": 49, "endColumn": 19}, {"ruleId": "718", "severity": 2, "message": "736", "line": 26, "column": 14, "nodeType": null, "messageId": "720", "endLine": 26, "endColumn": 18}, {"ruleId": "718", "severity": 2, "message": "722", "line": 76, "column": 14, "nodeType": null, "messageId": "720", "endLine": 76, "endColumn": 19}, {"ruleId": "737", "severity": 2, "message": "738", "line": 66, "column": 43, "nodeType": "739", "endLine": 66, "endColumn": 54}, {"ruleId": "737", "severity": 2, "message": "738", "line": 70, "column": 39, "nodeType": "739", "endLine": 70, "endColumn": 50}, {"ruleId": "737", "severity": 2, "message": "738", "line": 74, "column": 39, "nodeType": "739", "endLine": 74, "endColumn": 50}, {"ruleId": "737", "severity": 2, "message": "738", "line": 78, "column": 41, "nodeType": "739", "endLine": 78, "endColumn": 52}, {"ruleId": "737", "severity": 2, "message": "738", "line": 82, "column": 34, "nodeType": "739", "endLine": 82, "endColumn": 45}, {"ruleId": "737", "severity": 2, "message": "740", "line": 87, "column": 25, "nodeType": "739", "endLine": 87, "endColumn": 32}, {"ruleId": "727", "severity": 2, "message": "728", "line": 158, "column": 50, "nodeType": "729", "messageId": "730", "endLine": 158, "endColumn": 53, "suggestions": "741"}, {"ruleId": "718", "severity": 2, "message": "742", "line": 29, "column": 3, "nodeType": null, "messageId": "720", "endLine": 29, "endColumn": 9}, {"ruleId": "718", "severity": 2, "message": "743", "line": 48, "column": 3, "nodeType": null, "messageId": "720", "endLine": 48, "endColumn": 27}, {"ruleId": "718", "severity": 2, "message": "744", "line": 49, "column": 3, "nodeType": null, "messageId": "720", "endLine": 49, "endColumn": 29}, {"ruleId": "718", "severity": 2, "message": "745", "line": 52, "column": 15, "nodeType": null, "messageId": "720", "endLine": 52, "endColumn": 26}, {"ruleId": "727", "severity": 2, "message": "728", "line": 56, "column": 82, "nodeType": "729", "messageId": "730", "endLine": 56, "endColumn": 85, "suggestions": "746"}, {"ruleId": "718", "severity": 2, "message": "747", "line": 229, "column": 63, "nodeType": null, "messageId": "720", "endLine": 229, "endColumn": 68}, {"ruleId": "727", "severity": 2, "message": "728", "line": 231, "column": 36, "nodeType": "729", "messageId": "730", "endLine": 231, "endColumn": 39, "suggestions": "748"}, {"ruleId": "718", "severity": 2, "message": "747", "line": 243, "column": 72, "nodeType": null, "messageId": "720", "endLine": 243, "endColumn": 77}, {"ruleId": "727", "severity": 2, "message": "728", "line": 245, "column": 38, "nodeType": "729", "messageId": "730", "endLine": 245, "endColumn": 41, "suggestions": "749"}, {"ruleId": "718", "severity": 2, "message": "750", "line": 20, "column": 3, "nodeType": null, "messageId": "720", "endLine": 20, "endColumn": 11}, {"ruleId": "718", "severity": 2, "message": "751", "line": 27, "column": 30, "nodeType": null, "messageId": "720", "endLine": 27, "endColumn": 49}, {"ruleId": "727", "severity": 2, "message": "728", "line": 32, "column": 82, "nodeType": "729", "messageId": "730", "endLine": 32, "endColumn": 85, "suggestions": "752"}, {"ruleId": "718", "severity": 2, "message": "753", "line": 7, "column": 29, "nodeType": null, "messageId": "720", "endLine": 7, "endColumn": 44}, {"ruleId": "727", "severity": 2, "message": "728", "line": 84, "column": 68, "nodeType": "729", "messageId": "730", "endLine": 84, "endColumn": 71, "suggestions": "754"}, {"ruleId": "718", "severity": 2, "message": "755", "line": 16, "column": 3, "nodeType": null, "messageId": "720", "endLine": 16, "endColumn": 8}, {"ruleId": "718", "severity": 2, "message": "756", "line": 23, "column": 10, "nodeType": null, "messageId": "720", "endLine": 23, "endColumn": 12}, {"ruleId": "718", "severity": 2, "message": "757", "line": 48, "column": 9, "nodeType": null, "messageId": "720", "endLine": 48, "endColumn": 14}, {"ruleId": "718", "severity": 2, "message": "721", "line": 23, "column": 11, "nodeType": null, "messageId": "720", "endLine": 23, "endColumn": 12}, {"ruleId": "718", "severity": 2, "message": "758", "line": 8, "column": 29, "nodeType": null, "messageId": "720", "endLine": 8, "endColumn": 39}, {"ruleId": "718", "severity": 2, "message": "759", "line": 8, "column": 41, "nodeType": null, "messageId": "720", "endLine": 8, "endColumn": 50}, {"ruleId": "718", "severity": 2, "message": "760", "line": 25, "column": 3, "nodeType": null, "messageId": "720", "endLine": 25, "endColumn": 13}, {"ruleId": "718", "severity": 2, "message": "723", "line": 261, "column": 48, "nodeType": null, "messageId": "720", "endLine": 261, "endColumn": 53}, {"ruleId": "718", "severity": 2, "message": "761", "line": 32, "column": 11, "nodeType": null, "messageId": "720", "endLine": 32, "endColumn": 25}, {"ruleId": "718", "severity": 2, "message": "753", "line": 7, "column": 29, "nodeType": null, "messageId": "720", "endLine": 7, "endColumn": 44}, {"ruleId": "718", "severity": 2, "message": "758", "line": 7, "column": 46, "nodeType": null, "messageId": "720", "endLine": 7, "endColumn": 56}, {"ruleId": "718", "severity": 2, "message": "759", "line": 7, "column": 58, "nodeType": null, "messageId": "720", "endLine": 7, "endColumn": 67}, {"ruleId": "718", "severity": 2, "message": "750", "line": 20, "column": 3, "nodeType": null, "messageId": "720", "endLine": 20, "endColumn": 11}, {"ruleId": "718", "severity": 2, "message": "762", "line": 21, "column": 3, "nodeType": null, "messageId": "720", "endLine": 21, "endColumn": 7}, {"ruleId": "718", "severity": 2, "message": "763", "line": 22, "column": 3, "nodeType": null, "messageId": "720", "endLine": 22, "endColumn": 11}, {"ruleId": "718", "severity": 2, "message": "764", "line": 24, "column": 10, "nodeType": null, "messageId": "720", "endLine": 24, "endColumn": 19}, {"ruleId": "718", "severity": 2, "message": "765", "line": 2, "column": 10, "nodeType": null, "messageId": "720", "endLine": 2, "endColumn": 21}, {"ruleId": "718", "severity": 2, "message": "766", "line": 7, "column": 10, "nodeType": null, "messageId": "720", "endLine": 7, "endColumn": 16}, {"ruleId": "727", "severity": 2, "message": "728", "line": 108, "column": 50, "nodeType": "729", "messageId": "730", "endLine": 108, "endColumn": 53, "suggestions": "767"}, {"ruleId": "718", "severity": 2, "message": "768", "line": 19, "column": 3, "nodeType": null, "messageId": "720", "endLine": 19, "endColumn": 7}, {"ruleId": "727", "severity": 2, "message": "728", "line": 29, "column": 34, "nodeType": "729", "messageId": "730", "endLine": 29, "endColumn": 37, "suggestions": "769"}, {"ruleId": "718", "severity": 2, "message": "721", "line": 70, "column": 11, "nodeType": null, "messageId": "720", "endLine": 70, "endColumn": 12}, {"ruleId": "727", "severity": 2, "message": "728", "line": 104, "column": 80, "nodeType": "729", "messageId": "730", "endLine": 104, "endColumn": 83, "suggestions": "770"}, {"ruleId": "771", "severity": 2, "message": "772", "line": 284, "column": 46, "nodeType": "773", "messageId": "774", "suggestions": "775"}, {"ruleId": "771", "severity": 2, "message": "772", "line": 284, "column": 59, "nodeType": "773", "messageId": "774", "suggestions": "776"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 28, "column": 31, "nodeType": "729", "messageId": "730", "endLine": 28, "endColumn": 34, "suggestions": "777"}, {"ruleId": "771", "severity": 2, "message": "772", "line": 22, "column": 49, "nodeType": "773", "messageId": "774", "suggestions": "778"}, {"ruleId": "771", "severity": 2, "message": "772", "line": 22, "column": 64, "nodeType": "773", "messageId": "774", "suggestions": "779"}, {"ruleId": "771", "severity": 2, "message": "772", "line": 25, "column": 51, "nodeType": "773", "messageId": "774", "suggestions": "780"}, {"ruleId": "771", "severity": 2, "message": "772", "line": 25, "column": 68, "nodeType": "773", "messageId": "774", "suggestions": "781"}, {"ruleId": "771", "severity": 2, "message": "772", "line": 28, "column": 47, "nodeType": "773", "messageId": "774", "suggestions": "782"}, {"ruleId": "771", "severity": 2, "message": "772", "line": 28, "column": 60, "nodeType": "773", "messageId": "774", "suggestions": "783"}, {"ruleId": "718", "severity": 2, "message": "759", "line": 5, "column": 41, "nodeType": null, "messageId": "720", "endLine": 5, "endColumn": 50}, {"ruleId": "727", "severity": 2, "message": "728", "line": 24, "column": 28, "nodeType": "729", "messageId": "730", "endLine": 24, "endColumn": 31, "suggestions": "784"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 29, "column": 49, "nodeType": "729", "messageId": "730", "endLine": 29, "endColumn": 52, "suggestions": "785"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 48, "column": 32, "nodeType": "729", "messageId": "730", "endLine": 48, "endColumn": 35, "suggestions": "786"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 50, "column": 40, "nodeType": "729", "messageId": "730", "endLine": 50, "endColumn": 43, "suggestions": "787"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 61, "column": 59, "nodeType": "729", "messageId": "730", "endLine": 61, "endColumn": 62, "suggestions": "788"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 91, "column": 56, "nodeType": "729", "messageId": "730", "endLine": 91, "endColumn": 59, "suggestions": "789"}, {"ruleId": "718", "severity": 2, "message": "736", "line": 128, "column": 14, "nodeType": null, "messageId": "720", "endLine": 128, "endColumn": 18}, {"ruleId": "727", "severity": 2, "message": "728", "line": 293, "column": 32, "nodeType": "729", "messageId": "730", "endLine": 293, "endColumn": 35, "suggestions": "790"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 295, "column": 40, "nodeType": "729", "messageId": "730", "endLine": 295, "endColumn": 43, "suggestions": "791"}, {"ruleId": "718", "severity": 2, "message": "792", "line": 10, "column": 10, "nodeType": null, "messageId": "720", "endLine": 10, "endColumn": 15}, {"ruleId": "793", "severity": 1, "message": "794", "line": 84, "column": 6, "nodeType": "795", "endLine": 84, "endColumn": 38, "suggestions": "796"}, {"ruleId": "793", "severity": 1, "message": "794", "line": 89, "column": 6, "nodeType": "795", "endLine": 89, "endColumn": 15, "suggestions": "797"}, {"ruleId": "718", "severity": 2, "message": "798", "line": 7, "column": 27, "nodeType": null, "messageId": "720", "endLine": 7, "endColumn": 32}, {"ruleId": "793", "severity": 1, "message": "794", "line": 95, "column": 6, "nodeType": "795", "endLine": 95, "endColumn": 38, "suggestions": "799"}, {"ruleId": "793", "severity": 1, "message": "794", "line": 103, "column": 6, "nodeType": "795", "endLine": 103, "endColumn": 40, "suggestions": "800"}, {"ruleId": "718", "severity": 2, "message": "792", "line": 10, "column": 10, "nodeType": null, "messageId": "720", "endLine": 10, "endColumn": 15}, {"ruleId": "793", "severity": 1, "message": "794", "line": 79, "column": 6, "nodeType": "795", "endLine": 79, "endColumn": 38, "suggestions": "801"}, {"ruleId": "793", "severity": 1, "message": "794", "line": 84, "column": 6, "nodeType": "795", "endLine": 84, "endColumn": 15, "suggestions": "802"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 212, "column": 72, "nodeType": "729", "messageId": "730", "endLine": 212, "endColumn": 75, "suggestions": "803"}, {"ruleId": "718", "severity": 2, "message": "792", "line": 13, "column": 10, "nodeType": null, "messageId": "720", "endLine": 13, "endColumn": 15}, {"ruleId": "793", "severity": 1, "message": "794", "line": 76, "column": 6, "nodeType": "795", "endLine": 76, "endColumn": 38, "suggestions": "804"}, {"ruleId": "793", "severity": 1, "message": "794", "line": 81, "column": 6, "nodeType": "795", "endLine": 81, "endColumn": 15, "suggestions": "805"}, {"ruleId": "718", "severity": 2, "message": "806", "line": 7, "column": 23, "nodeType": null, "messageId": "720", "endLine": 7, "endColumn": 34}, {"ruleId": "718", "severity": 2, "message": "807", "line": 7, "column": 49, "nodeType": null, "messageId": "720", "endLine": 7, "endColumn": 55}, {"ruleId": "718", "severity": 2, "message": "808", "line": 15, "column": 10, "nodeType": null, "messageId": "720", "endLine": 15, "endColumn": 19}, {"ruleId": "718", "severity": 2, "message": "809", "line": 56, "column": 40, "nodeType": null, "messageId": "720", "endLine": 56, "endColumn": 46}, {"ruleId": "793", "severity": 1, "message": "794", "line": 67, "column": 6, "nodeType": "795", "endLine": 67, "endColumn": 38, "suggestions": "810"}, {"ruleId": "793", "severity": 1, "message": "794", "line": 72, "column": 6, "nodeType": "795", "endLine": 72, "endColumn": 15, "suggestions": "811"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 225, "column": 63, "nodeType": "729", "messageId": "730", "endLine": 225, "endColumn": 66, "suggestions": "812"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 274, "column": 50, "nodeType": "729", "messageId": "730", "endLine": 274, "endColumn": 53, "suggestions": "813"}, {"ruleId": "718", "severity": 2, "message": "814", "line": 17, "column": 3, "nodeType": null, "messageId": "720", "endLine": 17, "endColumn": 11}, {"ruleId": "718", "severity": 2, "message": "815", "line": 18, "column": 3, "nodeType": null, "messageId": "720", "endLine": 18, "endColumn": 9}, {"ruleId": "718", "severity": 2, "message": "816", "line": 19, "column": 3, "nodeType": null, "messageId": "720", "endLine": 19, "endColumn": 16}, {"ruleId": "718", "severity": 2, "message": "750", "line": 20, "column": 3, "nodeType": null, "messageId": "720", "endLine": 20, "endColumn": 11}, {"ruleId": "727", "severity": 2, "message": "728", "line": 28, "column": 9, "nodeType": "729", "messageId": "730", "endLine": 28, "endColumn": 12, "suggestions": "817"}, {"ruleId": "718", "severity": 2, "message": "818", "line": 57, "column": 25, "nodeType": null, "messageId": "720", "endLine": 57, "endColumn": 30}, {"ruleId": "718", "severity": 2, "message": "818", "line": 58, "column": 26, "nodeType": null, "messageId": "720", "endLine": 58, "endColumn": 31}, {"ruleId": "819", "severity": 2, "message": "820", "line": 26, "column": 11, "nodeType": "739", "messageId": "821", "endLine": 26, "endColumn": 29, "suggestions": "822"}, {"ruleId": "819", "severity": 2, "message": "820", "line": 5, "column": 18, "nodeType": "739", "messageId": "821", "endLine": 5, "endColumn": 28, "suggestions": "823"}, {"ruleId": "718", "severity": 2, "message": "721", "line": 16, "column": 17, "nodeType": null, "messageId": "720", "endLine": 16, "endColumn": 18}, {"ruleId": "718", "severity": 2, "message": "724", "line": 1, "column": 10, "nodeType": null, "messageId": "720", "endLine": 1, "endColumn": 18}, {"ruleId": "819", "severity": 2, "message": "820", "line": 5, "column": 18, "nodeType": "739", "messageId": "821", "endLine": 5, "endColumn": 31, "suggestions": "824"}, {"ruleId": "718", "severity": 2, "message": "825", "line": 18, "column": 7, "nodeType": null, "messageId": "826", "endLine": 18, "endColumn": 18}, {"ruleId": "718", "severity": 2, "message": "827", "line": 4, "column": 3, "nodeType": null, "messageId": "720", "endLine": 4, "endColumn": 18}, {"ruleId": "718", "severity": 2, "message": "828", "line": 13, "column": 3, "nodeType": null, "messageId": "720", "endLine": 13, "endColumn": 14}, {"ruleId": "718", "severity": 2, "message": "829", "line": 14, "column": 3, "nodeType": null, "messageId": "720", "endLine": 14, "endColumn": 21}, {"ruleId": "718", "severity": 2, "message": "830", "line": 15, "column": 3, "nodeType": null, "messageId": "720", "endLine": 15, "endColumn": 15}, {"ruleId": "718", "severity": 2, "message": "831", "line": 16, "column": 3, "nodeType": null, "messageId": "720", "endLine": 16, "endColumn": 22}, {"ruleId": "727", "severity": 2, "message": "728", "line": 344, "column": 27, "nodeType": "729", "messageId": "730", "endLine": 344, "endColumn": 30, "suggestions": "832"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 157, "column": 9, "nodeType": "729", "messageId": "730", "endLine": 157, "endColumn": 12, "suggestions": "833"}, {"ruleId": "834", "severity": 2, "message": "835", "line": 165, "column": 30, "nodeType": "836", "messageId": "837", "endLine": 165, "endColumn": 79}, {"ruleId": "727", "severity": 2, "message": "728", "line": 177, "column": 56, "nodeType": "729", "messageId": "730", "endLine": 177, "endColumn": 59, "suggestions": "838"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 179, "column": 56, "nodeType": "729", "messageId": "730", "endLine": 179, "endColumn": 59, "suggestions": "839"}, {"ruleId": "718", "severity": 2, "message": "840", "line": 187, "column": 18, "nodeType": null, "messageId": "720", "endLine": 187, "endColumn": 34}, {"ruleId": "727", "severity": 2, "message": "728", "line": 265, "column": 10, "nodeType": "729", "messageId": "730", "endLine": 265, "endColumn": 13, "suggestions": "841"}, {"ruleId": "718", "severity": 2, "message": "842", "line": 291, "column": 3, "nodeType": null, "messageId": "720", "endLine": 291, "endColumn": 4}, {"ruleId": "727", "severity": 2, "message": "728", "line": 307, "column": 9, "nodeType": "729", "messageId": "730", "endLine": 307, "endColumn": 12, "suggestions": "843"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 341, "column": 13, "nodeType": "729", "messageId": "730", "endLine": 341, "endColumn": 16, "suggestions": "844"}, {"ruleId": "718", "severity": 2, "message": "845", "line": 2, "column": 10, "nodeType": null, "messageId": "720", "endLine": 2, "endColumn": 23}, {"ruleId": "718", "severity": 2, "message": "846", "line": 11, "column": 7, "nodeType": null, "messageId": "720", "endLine": 11, "endColumn": 18}, {"ruleId": "718", "severity": 2, "message": "847", "line": 118, "column": 14, "nodeType": null, "messageId": "720", "endLine": 118, "endColumn": 24}, {"ruleId": "727", "severity": 2, "message": "728", "line": 197, "column": 54, "nodeType": "729", "messageId": "730", "endLine": 197, "endColumn": 57, "suggestions": "848"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 215, "column": 45, "nodeType": "729", "messageId": "730", "endLine": 215, "endColumn": 48, "suggestions": "849"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 219, "column": 39, "nodeType": "729", "messageId": "730", "endLine": 219, "endColumn": 42, "suggestions": "850"}, {"ruleId": "727", "severity": 2, "message": "728", "line": 228, "column": 53, "nodeType": "729", "messageId": "730", "endLine": 228, "endColumn": 56, "suggestions": "851"}, "@typescript-eslint/no-unused-vars", "'AssistanceType' is defined but never used.", "unusedVar", "'t' is assigned a value but never used.", "'error' is defined but never used.", "'index' is defined but never used.", "'useState' is defined but never used.", "'setDocumentTypes' is assigned a value but never used.", "'Settings' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["852", "853"], "'setUsers' is assigned a value but never used.", "'bcrypt' is defined but never used.", ["854", "855"], ["856", "857"], "'i18n' is assigned a value but never used.", "react-hooks/rules-of-hooks", "React Hook \"useCallback\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "Identifier", "React Hook \"useMemo\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", ["858", "859"], "'Filter' is defined but never used.", "'getBeneficiariesByStatus' is defined but never used.", "'getBeneficiariesByCategory' is defined but never used.", "'Beneficiary' is defined but never used.", ["860", "861"], "'label' is defined but never used.", ["862", "863"], ["864", "865"], "'Calendar' is defined but never used.", "'zakatCategoryLabels' is defined but never used.", ["866", "867"], "'CardDescription' is defined but never used.", ["868", "869"], "'Users' is defined but never used.", "'ar' is defined but never used.", "'stats' is assigned a value but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'CreditCard' is defined but never used.", "'hashedPassword' is assigned a value but never used.", "'User' is defined but never used.", "'FileText' is defined but never used.", "'mockTasks' is defined but never used.", "'NextRequest' is defined but never used.", "'Button' is defined but never used.", ["870", "871"], "'Home' is defined but never used.", ["872", "873"], ["874", "875"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["876", "877", "878", "879"], ["880", "881", "882", "883"], ["884", "885"], ["886", "887", "888", "889"], ["890", "891", "892", "893"], ["894", "895", "896", "897"], ["898", "899", "900", "901"], ["902", "903", "904", "905"], ["906", "907", "908", "909"], ["910", "911"], ["912", "913"], ["914", "915"], ["916", "917"], ["918", "919"], ["920", "921"], ["922", "923"], ["924", "925"], "'Label' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook React.useEffect has a missing dependency: 'onValidationChange'. Either include it or remove the dependency array. If 'onValidationChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["926"], ["927"], "'Check' is defined but never used.", ["928"], ["929"], ["930"], ["931"], ["932", "933"], ["934"], ["935"], "'AlertCircle' is defined but never used.", "'MapPin' is defined but never used.", "'Separator' is defined but never used.", "'errors' is assigned a value but never used.", ["936"], ["937"], ["938", "939"], ["940", "941"], "'Building' is defined but never used.", "'Shield' is defined but never used.", "'MessageSquare' is defined but never used.", ["942", "943"], "'props' is defined but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "noEmptyInterfaceWithSuper", ["944"], ["945"], ["946"], "'actionTypes' is assigned a value but only used as a type.", "usedOnlyAsType", "'PersonalProfile' is defined but never used.", "'CaseHistory' is defined but never used.", "'DistributionRecord' is defined but never used.", "'FamilyMember' is defined but never used.", "'BeneficiaryDocument' is defined but never used.", ["947", "948"], ["949", "950"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["951", "952"], ["953", "954"], "'translationError' is defined but never used.", ["955", "956"], "'t' is defined but never used.", ["957", "958"], ["959", "960"], "'ZakatCategory' is defined but never used.", "'EMAIL_REGEX' is assigned a value but never used.", "'categories' is defined but never used.", ["961", "962"], ["963", "964"], ["965", "966"], ["967", "968"], {"messageId": "969", "fix": "970", "desc": "971"}, {"messageId": "972", "fix": "973", "desc": "974"}, {"messageId": "969", "fix": "975", "desc": "971"}, {"messageId": "972", "fix": "976", "desc": "974"}, {"messageId": "969", "fix": "977", "desc": "971"}, {"messageId": "972", "fix": "978", "desc": "974"}, {"messageId": "969", "fix": "979", "desc": "971"}, {"messageId": "972", "fix": "980", "desc": "974"}, {"messageId": "969", "fix": "981", "desc": "971"}, {"messageId": "972", "fix": "982", "desc": "974"}, {"messageId": "969", "fix": "983", "desc": "971"}, {"messageId": "972", "fix": "984", "desc": "974"}, {"messageId": "969", "fix": "985", "desc": "971"}, {"messageId": "972", "fix": "986", "desc": "974"}, {"messageId": "969", "fix": "987", "desc": "971"}, {"messageId": "972", "fix": "988", "desc": "974"}, {"messageId": "969", "fix": "989", "desc": "971"}, {"messageId": "972", "fix": "990", "desc": "974"}, {"messageId": "969", "fix": "991", "desc": "971"}, {"messageId": "972", "fix": "992", "desc": "974"}, {"messageId": "969", "fix": "993", "desc": "971"}, {"messageId": "972", "fix": "994", "desc": "974"}, {"messageId": "969", "fix": "995", "desc": "971"}, {"messageId": "972", "fix": "996", "desc": "974"}, {"messageId": "997", "data": "998", "fix": "999", "desc": "1000"}, {"messageId": "997", "data": "1001", "fix": "1002", "desc": "1003"}, {"messageId": "997", "data": "1004", "fix": "1005", "desc": "1006"}, {"messageId": "997", "data": "1007", "fix": "1008", "desc": "1009"}, {"messageId": "997", "data": "1010", "fix": "1011", "desc": "1000"}, {"messageId": "997", "data": "1012", "fix": "1013", "desc": "1003"}, {"messageId": "997", "data": "1014", "fix": "1015", "desc": "1006"}, {"messageId": "997", "data": "1016", "fix": "1017", "desc": "1009"}, {"messageId": "969", "fix": "1018", "desc": "971"}, {"messageId": "972", "fix": "1019", "desc": "974"}, {"messageId": "997", "data": "1020", "fix": "1021", "desc": "1000"}, {"messageId": "997", "data": "1022", "fix": "1023", "desc": "1003"}, {"messageId": "997", "data": "1024", "fix": "1025", "desc": "1006"}, {"messageId": "997", "data": "1026", "fix": "1027", "desc": "1009"}, {"messageId": "997", "data": "1028", "fix": "1029", "desc": "1000"}, {"messageId": "997", "data": "1030", "fix": "1031", "desc": "1003"}, {"messageId": "997", "data": "1032", "fix": "1033", "desc": "1006"}, {"messageId": "997", "data": "1034", "fix": "1035", "desc": "1009"}, {"messageId": "997", "data": "1036", "fix": "1037", "desc": "1000"}, {"messageId": "997", "data": "1038", "fix": "1039", "desc": "1003"}, {"messageId": "997", "data": "1040", "fix": "1041", "desc": "1006"}, {"messageId": "997", "data": "1042", "fix": "1043", "desc": "1009"}, {"messageId": "997", "data": "1044", "fix": "1045", "desc": "1000"}, {"messageId": "997", "data": "1046", "fix": "1047", "desc": "1003"}, {"messageId": "997", "data": "1048", "fix": "1049", "desc": "1006"}, {"messageId": "997", "data": "1050", "fix": "1051", "desc": "1009"}, {"messageId": "997", "data": "1052", "fix": "1053", "desc": "1000"}, {"messageId": "997", "data": "1054", "fix": "1055", "desc": "1003"}, {"messageId": "997", "data": "1056", "fix": "1057", "desc": "1006"}, {"messageId": "997", "data": "1058", "fix": "1059", "desc": "1009"}, {"messageId": "997", "data": "1060", "fix": "1061", "desc": "1000"}, {"messageId": "997", "data": "1062", "fix": "1063", "desc": "1003"}, {"messageId": "997", "data": "1064", "fix": "1065", "desc": "1006"}, {"messageId": "997", "data": "1066", "fix": "1067", "desc": "1009"}, {"messageId": "969", "fix": "1068", "desc": "971"}, {"messageId": "972", "fix": "1069", "desc": "974"}, {"messageId": "969", "fix": "1070", "desc": "971"}, {"messageId": "972", "fix": "1071", "desc": "974"}, {"messageId": "969", "fix": "1072", "desc": "971"}, {"messageId": "972", "fix": "1073", "desc": "974"}, {"messageId": "969", "fix": "1074", "desc": "971"}, {"messageId": "972", "fix": "1075", "desc": "974"}, {"messageId": "969", "fix": "1076", "desc": "971"}, {"messageId": "972", "fix": "1077", "desc": "974"}, {"messageId": "969", "fix": "1078", "desc": "971"}, {"messageId": "972", "fix": "1079", "desc": "974"}, {"messageId": "969", "fix": "1080", "desc": "971"}, {"messageId": "972", "fix": "1081", "desc": "974"}, {"messageId": "969", "fix": "1082", "desc": "971"}, {"messageId": "972", "fix": "1083", "desc": "974"}, {"desc": "1084", "fix": "1085"}, {"desc": "1086", "fix": "1087"}, {"desc": "1084", "fix": "1088"}, {"desc": "1089", "fix": "1090"}, {"desc": "1084", "fix": "1091"}, {"desc": "1086", "fix": "1092"}, {"messageId": "969", "fix": "1093", "desc": "971"}, {"messageId": "972", "fix": "1094", "desc": "974"}, {"desc": "1084", "fix": "1095"}, {"desc": "1086", "fix": "1096"}, {"desc": "1084", "fix": "1097"}, {"desc": "1086", "fix": "1098"}, {"messageId": "969", "fix": "1099", "desc": "971"}, {"messageId": "972", "fix": "1100", "desc": "974"}, {"messageId": "969", "fix": "1101", "desc": "971"}, {"messageId": "972", "fix": "1102", "desc": "974"}, {"messageId": "969", "fix": "1103", "desc": "971"}, {"messageId": "972", "fix": "1104", "desc": "974"}, {"messageId": "1105", "fix": "1106", "desc": "1107"}, {"messageId": "1105", "fix": "1108", "desc": "1107"}, {"messageId": "1105", "fix": "1109", "desc": "1107"}, {"messageId": "969", "fix": "1110", "desc": "971"}, {"messageId": "972", "fix": "1111", "desc": "974"}, {"messageId": "969", "fix": "1112", "desc": "971"}, {"messageId": "972", "fix": "1113", "desc": "974"}, {"messageId": "969", "fix": "1114", "desc": "971"}, {"messageId": "972", "fix": "1115", "desc": "974"}, {"messageId": "969", "fix": "1116", "desc": "971"}, {"messageId": "972", "fix": "1117", "desc": "974"}, {"messageId": "969", "fix": "1118", "desc": "971"}, {"messageId": "972", "fix": "1119", "desc": "974"}, {"messageId": "969", "fix": "1120", "desc": "971"}, {"messageId": "972", "fix": "1121", "desc": "974"}, {"messageId": "969", "fix": "1122", "desc": "971"}, {"messageId": "972", "fix": "1123", "desc": "974"}, {"messageId": "969", "fix": "1124", "desc": "971"}, {"messageId": "972", "fix": "1125", "desc": "974"}, {"messageId": "969", "fix": "1126", "desc": "971"}, {"messageId": "972", "fix": "1127", "desc": "974"}, {"messageId": "969", "fix": "1128", "desc": "971"}, {"messageId": "972", "fix": "1129", "desc": "974"}, {"messageId": "969", "fix": "1130", "desc": "971"}, {"messageId": "972", "fix": "1131", "desc": "974"}, "suggestUnknown", {"range": "1132", "text": "1133"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1134", "text": "1135"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1136", "text": "1133"}, {"range": "1137", "text": "1135"}, {"range": "1138", "text": "1133"}, {"range": "1139", "text": "1135"}, {"range": "1140", "text": "1133"}, {"range": "1141", "text": "1135"}, {"range": "1142", "text": "1133"}, {"range": "1143", "text": "1135"}, {"range": "1144", "text": "1133"}, {"range": "1145", "text": "1135"}, {"range": "1146", "text": "1133"}, {"range": "1147", "text": "1135"}, {"range": "1148", "text": "1133"}, {"range": "1149", "text": "1135"}, {"range": "1150", "text": "1133"}, {"range": "1151", "text": "1135"}, {"range": "1152", "text": "1133"}, {"range": "1153", "text": "1135"}, {"range": "1154", "text": "1133"}, {"range": "1155", "text": "1135"}, {"range": "1156", "text": "1133"}, {"range": "1157", "text": "1135"}, "replaceWithAlt", {"alt": "1158"}, {"range": "1159", "text": "1160"}, "Replace with `&quot;`.", {"alt": "1161"}, {"range": "1162", "text": "1163"}, "Replace with `&ldquo;`.", {"alt": "1164"}, {"range": "1165", "text": "1166"}, "Replace with `&#34;`.", {"alt": "1167"}, {"range": "1168", "text": "1169"}, "Replace with `&rdquo;`.", {"alt": "1158"}, {"range": "1170", "text": "1171"}, {"alt": "1161"}, {"range": "1172", "text": "1173"}, {"alt": "1164"}, {"range": "1174", "text": "1175"}, {"alt": "1167"}, {"range": "1176", "text": "1177"}, {"range": "1178", "text": "1133"}, {"range": "1179", "text": "1135"}, {"alt": "1158"}, {"range": "1180", "text": "1181"}, {"alt": "1161"}, {"range": "1182", "text": "1183"}, {"alt": "1164"}, {"range": "1184", "text": "1185"}, {"alt": "1167"}, {"range": "1186", "text": "1187"}, {"alt": "1158"}, {"range": "1188", "text": "1189"}, {"alt": "1161"}, {"range": "1190", "text": "1191"}, {"alt": "1164"}, {"range": "1192", "text": "1193"}, {"alt": "1167"}, {"range": "1194", "text": "1195"}, {"alt": "1158"}, {"range": "1196", "text": "1181"}, {"alt": "1161"}, {"range": "1197", "text": "1183"}, {"alt": "1164"}, {"range": "1198", "text": "1185"}, {"alt": "1167"}, {"range": "1199", "text": "1187"}, {"alt": "1158"}, {"range": "1200", "text": "1189"}, {"alt": "1161"}, {"range": "1201", "text": "1191"}, {"alt": "1164"}, {"range": "1202", "text": "1193"}, {"alt": "1167"}, {"range": "1203", "text": "1195"}, {"alt": "1158"}, {"range": "1204", "text": "1181"}, {"alt": "1161"}, {"range": "1205", "text": "1183"}, {"alt": "1164"}, {"range": "1206", "text": "1185"}, {"alt": "1167"}, {"range": "1207", "text": "1187"}, {"alt": "1158"}, {"range": "1208", "text": "1189"}, {"alt": "1161"}, {"range": "1209", "text": "1191"}, {"alt": "1164"}, {"range": "1210", "text": "1193"}, {"alt": "1167"}, {"range": "1211", "text": "1195"}, {"range": "1212", "text": "1133"}, {"range": "1213", "text": "1135"}, {"range": "1214", "text": "1133"}, {"range": "1215", "text": "1135"}, {"range": "1216", "text": "1133"}, {"range": "1217", "text": "1135"}, {"range": "1218", "text": "1133"}, {"range": "1219", "text": "1135"}, {"range": "1220", "text": "1133"}, {"range": "1221", "text": "1135"}, {"range": "1222", "text": "1133"}, {"range": "1223", "text": "1135"}, {"range": "1224", "text": "1133"}, {"range": "1225", "text": "1135"}, {"range": "1226", "text": "1133"}, {"range": "1227", "text": "1135"}, "Update the dependencies array to be: [watch, updateFormData, isValid, onValidationChange]", {"range": "1228", "text": "1229"}, "Update the dependencies array to be: [isValid, onValidationChange]", {"range": "1230", "text": "1231"}, {"range": "1232", "text": "1229"}, "Update the dependencies array to be: [uploadedDocuments, form, isValid, onValidationChange]", {"range": "1233", "text": "1234"}, {"range": "1235", "text": "1229"}, {"range": "1236", "text": "1231"}, {"range": "1237", "text": "1133"}, {"range": "1238", "text": "1135"}, {"range": "1239", "text": "1229"}, {"range": "1240", "text": "1231"}, {"range": "1241", "text": "1229"}, {"range": "1242", "text": "1231"}, {"range": "1243", "text": "1133"}, {"range": "1244", "text": "1135"}, {"range": "1245", "text": "1133"}, {"range": "1246", "text": "1135"}, {"range": "1247", "text": "1133"}, {"range": "1248", "text": "1135"}, "replaceEmptyInterfaceWithSuper", {"range": "1249", "text": "1250"}, "Replace empty interface with a type alias.", {"range": "1251", "text": "1252"}, {"range": "1253", "text": "1254"}, {"range": "1255", "text": "1133"}, {"range": "1256", "text": "1135"}, {"range": "1257", "text": "1133"}, {"range": "1258", "text": "1135"}, {"range": "1259", "text": "1133"}, {"range": "1260", "text": "1135"}, {"range": "1261", "text": "1133"}, {"range": "1262", "text": "1135"}, {"range": "1263", "text": "1133"}, {"range": "1264", "text": "1135"}, {"range": "1265", "text": "1133"}, {"range": "1266", "text": "1135"}, {"range": "1267", "text": "1133"}, {"range": "1268", "text": "1135"}, {"range": "1269", "text": "1133"}, {"range": "1270", "text": "1135"}, {"range": "1271", "text": "1133"}, {"range": "1272", "text": "1135"}, {"range": "1273", "text": "1133"}, {"range": "1274", "text": "1135"}, {"range": "1275", "text": "1133"}, {"range": "1276", "text": "1135"}, [2204, 2207], "unknown", [2204, 2207], "never", [1469, 1472], [1469, 1472], [1689, 1692], [1689, 1692], [5499, 5502], [5499, 5502], [1385, 1388], [1385, 1388], [8288, 8291], [8288, 8291], [8990, 8993], [8990, 8993], [984, 987], [984, 987], [2731, 2734], [2731, 2734], [3763, 3766], [3763, 3766], [559, 562], [559, 562], [3123, 3126], [3123, 3126], "&quot;", [9678, 9713], "Click &quot;Add Document\" to get started", "&ldquo;", [9678, 9713], "Click &ldquo;Add Document\" to get started", "&#34;", [9678, 9713], "Click &#34;Add Document\" to get started", "&rdquo;", [9678, 9713], "Click &rdquo;Add Document\" to get started", [9678, 9713], "Click \"Add Document&quot; to get started", [9678, 9713], "Click \"Add Document&ldquo; to get started", [9678, 9713], "Click \"Add Document&#34; to get started", [9678, 9713], "Click \"Add Document&rdquo; to get started", [735, 738], [735, 738], [635, 637], " &quot;", [635, 637], " &ldquo;", [635, 637], " &#34;", [635, 637], " &rdquo;", [651, 661], "&quot;\n        ", [651, 661], "&ldquo;\n        ", [651, 661], "&#34;\n        ", [651, 661], "&rdquo;\n        ", [731, 733], [731, 733], [731, 733], [731, 733], [749, 759], [749, 759], [749, 759], [749, 759], [825, 827], [825, 827], [825, 827], [825, 827], [839, 849], [839, 849], [839, 849], [839, 849], [712, 715], [712, 715], [901, 904], [901, 904], [1404, 1407], [1404, 1407], [1493, 1496], [1493, 1496], [1779, 1782], [1779, 1782], [2516, 2519], [2516, 2519], [8334, 8337], [8334, 8337], [8423, 8426], [8423, 8426], [3127, 3159], "[watch, updateFormData, isValid, onValidationChange]", [3274, 3283], "[is<PERSON><PERSON>d, onValidationChange]", [3190, 3222], [3567, 3601], "[uploadedDocuments, form, isValid, onValidationChange]", [2618, 2650], [2765, 2774], [7839, 7842], [7839, 7842], [2366, 2398], [2513, 2522], [2147, 2179], [2294, 2303], [8392, 8395], [8392, 8395], [10487, 10490], [10487, 10490], [509, 512], [509, 512], [724, 775], "type CommandDialogProps = DialogProps", [73, 150], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [75, 161], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [7694, 7697], [7694, 7697], [4189, 4192], [4189, 4192], [4890, 4893], [4890, 4893], [5026, 5029], [5026, 5029], [7292, 7295], [7292, 7295], [8276, 8279], [8276, 8279], [9285, 9288], [9285, 9288], [5356, 5359], [5356, 5359], [5972, 5975], [5972, 5975], [6097, 6100], [6097, 6100], [6302, 6305], [6302, 6305]]